'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Loading } from '@/components/ui';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const {  loading, isInitialized } = useAuth();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || loading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const breadcrumbs = [
    { label: 'Home', href: '/client/dashboard' },
  ];

  if (pathname !== '/client/dashboard') {
    const pathParts = pathname.split('/').filter(Boolean);
    if (pathParts.length > 1) {
      const currentPage = pathParts[pathParts.length - 1];
      breadcrumbs.push({
        label: currentPage.charAt(0).toUpperCase() + currentPage.slice(1).replace(/-/g, ' '),
        href: pathname,
      });
    }
  }

  return (
    <ErrorBoundary>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </ErrorBoundary>
  );
}
