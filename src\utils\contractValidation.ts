import { 
  ContractStatus, 
  Contract, 
  ExtendedContract,
  CreateWorkSubmissionDto,
} from '@/types/features/contracts/contract.types';
import { UserRole } from '@/types/features/auth/auth.types';

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  code?: string;
}

export interface ContractValidationContext {
  contract: Contract | ExtendedContract;
  userRole: UserRole;
  userId: string;
}

/**
 * Validates if a contract status transition is allowed
 */
export const validateStatusTransition = (
  fromStatus: ContractStatus,
  toStatus: ContractStatus,
  userRole: UserRole
): ValidationResult => {
  const allowedTransitions: Record<ContractStatus, Partial<Record<UserRole, ContractStatus[]>>> = {
    [ContractStatus.DRAFT]: {
      [UserRole.CLIENT]: [ContractStatus.PENDING_FREELANCER_ACCEPTANCE, ContractStatus.CANCELLED]
    },
    [ContractStatus.PENDING_FREELANCER_ACCEPTANCE]: {
      [UserRole.FREELANCER]: [ContractStatus.ACTIVE, ContractStatus.CANCELLED]
    },
    [ContractStatus.ACTIVE]: {
      [UserRole.FREELANCER]: [ContractStatus.WORK_SUBMITTED],
      [UserRole.CLIENT]: [ContractStatus.CANCELLED]
    },
    [ContractStatus.WORK_SUBMITTED]: {
      [UserRole.CLIENT]: [ContractStatus.COMPLETED, ContractStatus.REVISIONS_REQUESTED]
    },
    [ContractStatus.REVISIONS_REQUESTED]: {
      [UserRole.FREELANCER]: [ContractStatus.WORK_SUBMITTED],
      [UserRole.CLIENT]: [ContractStatus.CANCELLED]
    },
    [ContractStatus.COMPLETED]: {
      [UserRole.CLIENT]: [ContractStatus.PAID, ContractStatus.REVISIONS_REQUESTED]
    },
    [ContractStatus.PAID]: {
      // No transitions allowed from PAID status
    },
    [ContractStatus.CANCELLED]: {
      // No transitions allowed from CANCELLED status
    },
    [ContractStatus.DISPUTED]: {
      [UserRole.CLIENT]: [ContractStatus.ACTIVE, ContractStatus.CANCELLED],
      [UserRole.FREELANCER]: [ContractStatus.ACTIVE, ContractStatus.CANCELLED]
    }
  };

  const allowedForRole = allowedTransitions[fromStatus]?.[userRole];
  
  if (!allowedForRole || !allowedForRole.includes(toStatus)) {
    return {
      isValid: false,
      message: `Cannot transition from ${fromStatus} to ${toStatus} as ${userRole}`,
      code: 'INVALID_STATUS_TRANSITION'
    };
  }

  return { isValid: true };
};

/**
 * Validates if a user can perform a specific action on a contract
 */
export const validateContractAction = (
  action: string,
  context: ContractValidationContext
): ValidationResult => {
  const { contract, userRole, userId } = context;

  // Check if user is part of the contract
  if (userId !== contract.clientId && userId !== contract.freelancerId) {
    return {
      isValid: false,
      message: 'You are not authorized to perform actions on this contract',
      code: 'UNAUTHORIZED'
    };
  }

  // Check role-specific permissions
  const isClient = userRole === UserRole.CLIENT && userId === contract.clientId;
  const isFreelancer = userRole === UserRole.FREELANCER && userId === contract.freelancerId;

  switch (action) {
    case 'accept':
      if (!isFreelancer || contract.status !== ContractStatus.PENDING_FREELANCER_ACCEPTANCE) {
        return {
          isValid: false,
          message: 'Only freelancers can accept contracts pending acceptance',
          code: 'INVALID_ACTION'
        };
      }
      break;

    case 'reject':
      if (!isFreelancer || contract.status !== ContractStatus.PENDING_FREELANCER_ACCEPTANCE) {
        return {
          isValid: false,
          message: 'Only freelancers can reject contracts pending acceptance',
          code: 'INVALID_ACTION'
        };
      }
      break;

    case 'submitWork':
      if (!isFreelancer || ![ContractStatus.ACTIVE, ContractStatus.REVISIONS_REQUESTED].includes(contract.status)) {
        return {
          isValid: false,
          message: 'Work can only be submitted when contract is active or revisions are requested',
          code: 'INVALID_ACTION'
        };
      }
      break;

    case 'approveWork':
      if (!isClient || contract.status !== ContractStatus.WORK_SUBMITTED) {
        return {
          isValid: false,
          message: 'Only clients can approve submitted work',
          code: 'INVALID_ACTION'
        };
      }
      break;

    case 'requestRevisions':
      if (!isClient || contract.status !== ContractStatus.WORK_SUBMITTED) {
        return {
          isValid: false,
          message: 'Revisions can only be requested for submitted work',
          code: 'INVALID_ACTION'
        };
      }
      break;

    case 'markPaid':
      if (!isClient || contract.status !== ContractStatus.COMPLETED) {
        return {
          isValid: false,
          message: 'Only clients can mark completed contracts as paid',
          code: 'INVALID_ACTION'
        };
      }
      break;

    case 'cancel':
      if (![ContractStatus.ACTIVE, ContractStatus.REVISIONS_REQUESTED].includes(contract.status)) {
        return {
          isValid: false,
          message: 'Contract can only be cancelled when active or during revisions',
          code: 'INVALID_ACTION'
        };
      }
      break;

    default:
      return {
        isValid: false,
        message: 'Unknown action',
        code: 'UNKNOWN_ACTION'
      };
  }

  return { isValid: true };
};

/**
 * Validates work submission data
 */
export const validateWorkSubmission = (submission: CreateWorkSubmissionDto): ValidationResult => {
  if (!submission.description || submission.description.trim().length < 10) {
    return {
      isValid: false,
      message: 'Work description must be at least 10 characters long',
      code: 'INVALID_DESCRIPTION'
    };
  }

  if (!submission.attachments?.length && !submission.links?.length) {
    return {
      isValid: false,
      message: 'At least one attachment or link must be provided',
      code: 'NO_DELIVERABLES'
    };
  }

  // Validate links if provided
  if (submission.links?.length) {
    const urlPattern = /^https?:\/\/.+/;
    const invalidLinks = submission.links.filter(link => !urlPattern.test(link));
    
    if (invalidLinks.length > 0) {
      return {
        isValid: false,
        message: 'All links must be valid URLs starting with http:// or https://',
        code: 'INVALID_LINKS'
      };
    }
  }

  return { isValid: true };
};

/**
 * Validates contract data before creation or update
 */
export const validateContractData = (contract: Partial<Contract>): ValidationResult => {
  if (!contract.title || contract.title.trim().length < 3) {
    return {
      isValid: false,
      message: 'Contract title must be at least 3 characters long',
      code: 'INVALID_TITLE'
    };
  }

  if (!contract.description || contract.description.trim().length < 10) {
    return {
      isValid: false,
      message: 'Contract description must be at least 10 characters long',
      code: 'INVALID_DESCRIPTION'
    };
  }

  if (!contract.budget || contract.budget <= 0) {
    return {
      isValid: false,
      message: 'Contract budget must be greater than 0',
      code: 'INVALID_BUDGET'
    };
  }

  if (!contract.clientId || !contract.freelancerId) {
    return {
      isValid: false,
      message: 'Both client and freelancer must be specified',
      code: 'MISSING_PARTIES'
    };
  }

  if (contract.clientId === contract.freelancerId) {
    return {
      isValid: false,
      message: 'Client and freelancer cannot be the same person',
      code: 'SAME_PARTIES'
    };
  }

  return { isValid: true };
};

/**
 * Gets user-friendly error messages for common validation errors
 */
export const getValidationErrorMessage = (code: string): string => {
  const messages: Record<string, string> = {
    INVALID_STATUS_TRANSITION: 'This action is not allowed for the current contract status.',
    UNAUTHORIZED: 'You do not have permission to perform this action.',
    INVALID_ACTION: 'This action cannot be performed at this time.',
    UNKNOWN_ACTION: 'The requested action is not recognized.',
    INVALID_DESCRIPTION: 'Please provide a more detailed description.',
    NO_DELIVERABLES: 'Please provide at least one file or link with your submission.',
    INVALID_LINKS: 'Please ensure all links are valid URLs.',
    INVALID_TITLE: 'Please provide a more descriptive title.',
    INVALID_BUDGET: 'Please enter a valid budget amount.',
    MISSING_PARTIES: 'Contract must have both a client and freelancer.',
    SAME_PARTIES: 'Client and freelancer must be different people.'
  };

  return messages[code] || 'A validation error occurred. Please check your input and try again.';
};

/**
 * Checks if a contract is in a final state (no further actions possible)
 */
export const isContractFinal = (status: ContractStatus): boolean => {
  return [ContractStatus.PAID, ContractStatus.CANCELLED].includes(status);
};

/**
 * Gets the next expected action for a contract based on its status and user role
 */
export const getNextExpectedAction = (
  contract: Contract | ExtendedContract,
  userRole: UserRole,
  userId: string
): string | null => {
  const isClient = userRole === UserRole.CLIENT && userId === contract.clientId;
  const isFreelancer = userRole === UserRole.FREELANCER && userId === contract.freelancerId;

  switch (contract.status) {
    case ContractStatus.PENDING_FREELANCER_ACCEPTANCE:
      return isFreelancer ? 'Accept or decline the contract' : 'Waiting for freelancer response';
    
    case ContractStatus.ACTIVE:
      return isFreelancer ? 'Submit your completed work' : 'Waiting for work submission';
    
    case ContractStatus.WORK_SUBMITTED:
      return isClient ? 'Review and approve or request changes' : 'Waiting for client review';
    
    case ContractStatus.REVISIONS_REQUESTED:
      return isFreelancer ? 'Submit revised work' : 'Waiting for revised submission';
    
    case ContractStatus.COMPLETED:
      return isClient ? 'Process payment' : 'Waiting for payment';
    
    case ContractStatus.PAID:
      return 'Contract completed successfully';
    
    case ContractStatus.CANCELLED:
      return 'Contract was cancelled';
    
    case ContractStatus.DISPUTED:
      return 'Resolve dispute through support';
    
    default:
      return null;
  }
};
