import React from 'react';
import { Badge, BadgeProps } from '@/components/ui/Badge';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { cn } from '@/lib/utils';

const statusVariants: Record<ContractStatus, string> = {
  DRAFT: 'bg-gray-100 text-gray-800 hover:bg-gray-100',
  PENDING_FREELANCER_ACCEPTANCE: 'bg-orange-100 text-orange-800 hover:bg-orange-100',
  ACTIVE: 'bg-blue-100 text-blue-800 hover:bg-blue-100',
  WORK_SUBMITTED: 'bg-purple-100 text-purple-800 hover:bg-purple-100',
  REVISIONS_REQUESTED: 'bg-amber-100 text-amber-800 hover:bg-amber-100',
  COMPLETED: 'bg-green-100 text-green-800 hover:bg-green-100',
  PAID: 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100',
  CANCELLED: 'bg-red-100 text-red-800 hover:bg-red-100',
  DISPUTED: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
};

const statusLabels: Record<ContractStatus, string> = {
  DRAFT: 'Draft',
  PENDING_FREELANCER_ACCEPTANCE: 'Pending Acceptance',
  ACTIVE: 'Active',
  WORK_SUBMITTED: 'Work Submitted',
  REVISIONS_REQUESTED: 'Revisions Requested',
  COMPLETED: 'Completed',
  PAID: 'Paid',
  CANCELLED: 'Cancelled',
  DISPUTED: 'In Dispute',
};

interface ContractStatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: ContractStatus;
  className?: string;
}

export const ContractStatusBadge: React.FC<ContractStatusBadgeProps> = ({
  status,
  className = '',
  ...props
}) => {
  return (
    <Badge
      className={cn(
        'whitespace-nowrap',
        statusVariants[status],
        className
      )}
      {...props}
    >
      {statusLabels[status]}
    </Badge>
  );
};

export default ContractStatusBadge;
