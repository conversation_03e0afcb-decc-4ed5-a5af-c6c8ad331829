"use client";
import { safeClient } from './graphql/safeClient';
import * as queries from "../lib/graphql/queries";
import * as mutations from "../lib/graphql/mutations";
import type { 
  User, 
  CreateUserInput, 
  UpdateUserInput,
  UserRole 
} from "@/types/user";

export type { 
  User, 
  CreateUserInput, 
  UpdateUserInput, 
  UserRole 
};

export async function getUserById(id: string): Promise<User | null> {
  try {
    const result = await safeClient.execute<{ getUser: User | null }>({
      query: queries.getUser,
      variables: { id },
      authMode: 'AMAZON_COGNITO_USER_POOLS'
    });
    
    if (!result.getUser) {
      throw new Error('User not found');
    }
    
    return result.getUser;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
}

export async function createUser(input: CreateUserInput): Promise<User> {
  try {
    const result = await safeClient.execute<{ createUser: User }>({
      query: mutations.createUser,
      variables: { input },
      authMode: 'AMAZON_COGNITO_USER_POOLS'
    });
    
    if (!result.createUser) {
      throw new Error('Failed to create user');
    }
    
    return result.createUser;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
}

export async function updateUser(input: UpdateUserInput): Promise<User> {
  try {
    const result = await safeClient.execute<{ updateUser: User }>({
      query: mutations.updateUser,
      variables: { input },
      authMode: 'AMAZON_COGNITO_USER_POOLS'
    });
    
    if (!result.updateUser) {
      throw new Error('Failed to update user');
    }
    
    return result.updateUser;
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
}
