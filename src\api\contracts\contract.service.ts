import {
  Contract,
  ContractFilters,
  ContractStatus,
  CreateContractDto,
  UpdateContractDto,
  WorkSubmission,
  CreateWorkSubmissionDto,
  ReviewWorkSubmissionDto,
  PaymentSchedule,
  Deliverable
} from '@/types/features/contracts/contract.types';
import { JobStatus } from '@/types/features/jobs/job.types';
import { contractApi } from './contract.api';

function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const contractService = {
  async getContract(id: string): Promise<Contract> {
    try {
      return await contractApi.getContract(id);
    } catch (error) {
      return handleApiError('getContract', error);
    }
  },

  async listContracts(
    filters?: ContractFilters,
    limit: number = 10,
    nextToken?: string
  ): Promise<{ items: Contract[]; nextToken?: string }> {
    try {
      return await contractApi.listContracts(filters, limit, nextToken);
    } catch (error) {
      return handleApiError('listContracts', error);
    }
  },

  async getContractById(id: string): Promise<Contract> {
    try {
      return await contractApi.getContract(id);
    } catch (error) {
      return handleApiError('getContractById', error);
    }
  },

  async getContractsByJob(jobId: string): Promise<Contract[]> {
    try {
      return await contractApi.getContractsByJob(jobId);
    } catch (error) {
      return handleApiError('getContractsByJob', error);
    }
  },

  async getContractByProposal(proposalId: string): Promise<Contract | null> {
    try {
      const { items } = await contractApi.listContracts({ proposalId });
      return items.length > 0 ? items[0] : null;
    } catch (error) {
      console.error('Error checking contract by proposal:', error);
      return null;
    }
  },

  async hasExistingContract(proposalId: string): Promise<boolean> {
    try {
      const contract = await this.getContractByProposal(proposalId);
      return contract !== null;
    } catch (error) {
      console.error('Error checking existing contract:', error);
      return false;
    }
  },

  async getUserContracts(
    userId: string,
    status?: ContractStatus
  ): Promise<Contract[]> {
    try {
      return await contractApi.getUserContracts(userId, status);
    } catch (error) {
      return handleApiError('getUserContracts', error);
    }
  },

  async createContract(input: CreateContractDto): Promise<Contract> {
    try {
      return await contractApi.createContract(input);
    } catch (error) {
      return handleApiError('createContract', error);
    }
  },

  async updateContract(id: string, input: UpdateContractDto): Promise<Contract> {
    try {
      const updateData: UpdateContractDto & { id: string } = {
        ...input,
        id
      };
      return await contractApi.updateContract(updateData);
    } catch (error) {
      return handleApiError('updateContract', error);
    }
  },

  async updateContractStatus(id: string, status: ContractStatus): Promise<Contract> {
    try {
      return await contractApi.updateContractStatus(id, status);
    } catch (error) {
      return handleApiError('updateContractStatus', error);
    }
  },

  async acceptContract(id: string): Promise<Contract> {
    try {
      return await contractApi.acceptContract(id);
    } catch (error) {
      return handleApiError('acceptContract', error);
    }
  },

  async rejectContract(id: string): Promise<Contract> {
    try {
      return await contractApi.rejectContract(id);
    } catch (error) {
      return handleApiError('rejectContract', error);
    }
  },

  async completeContract(id: string): Promise<Contract> {
    try {
      return await contractApi.completeContract(id);
    } catch (error) {
      return handleApiError('completeContract', error);
    }
  },

  // Work submission operations
  async createWorkSubmission(submission: CreateWorkSubmissionDto): Promise<WorkSubmission> {
    try {
      return await contractApi.createWorkSubmission(submission);
    } catch (error) {
      return handleApiError('createWorkSubmission', error);
    }
  },

  async updateWorkSubmission(id: string, updates: Omit<ReviewWorkSubmissionDto, 'id'>): Promise<WorkSubmission> {
    try {
      return await contractApi.updateWorkSubmission(id, updates);
    } catch (error) {
      return handleApiError('updateWorkSubmission', error);
    }
  },

  async getContractWorkSubmissions(contractId: string): Promise<WorkSubmission[]> {
    try {
      return await contractApi.getContractWorkSubmissions(contractId);
    } catch (error) {
      return handleApiError('getContractWorkSubmissions', error);
    }
  },

  // Job status operations
  async updateJobStatus(jobId: string, status: JobStatus): Promise<{ id: string; status: JobStatus }> {
    try {
      return await contractApi.updateJobStatus(jobId, status);
    } catch (error) {
      return handleApiError('updateJobStatus', error);
    }
  },

  // Payment operations
  async createPayment(contractId: string, amount: number, method: string = 'STRIPE'): Promise<any> {
    try {
      return await contractApi.createPayment(contractId, amount, method);
    } catch (error) {
      return handleApiError('createPayment', error);
    }
  },

  async getContractPayments(contractId: string): Promise<any[]> {
    try {
      return await contractApi.getContractPayments(contractId);
    } catch (error) {
      return handleApiError('getContractPayments', error);
    }
  },

  // Payment schedule operations
  async createPaymentSchedule(contractId: string, payment: Omit<PaymentSchedule, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>): Promise<PaymentSchedule> {
    try {
      return await contractApi.createPaymentSchedule(contractId, payment);
    } catch (error) {
      return handleApiError('createPaymentSchedule', error);
    }
  },

  async updatePaymentSchedule(id: string, updates: Partial<Omit<PaymentSchedule, 'id' | 'contractId' | 'createdAt'>>): Promise<PaymentSchedule> {
    try {
      return await contractApi.updatePaymentSchedule(id, updates);
    } catch (error) {
      return handleApiError('updatePaymentSchedule', error);
    }
  },

  async getContractPaymentSchedules(contractId: string): Promise<PaymentSchedule[]> {
    try {
      return await contractApi.getContractPaymentSchedules(contractId);
    } catch (error) {
      return handleApiError('getContractPaymentSchedules', error);
    }
  },

  // Deliverable operations
  async createDeliverable(contractId: string, deliverable: Omit<Deliverable, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>): Promise<Deliverable> {
    try {
      return await contractApi.createDeliverable(contractId, deliverable);
    } catch (error) {
      return handleApiError('createDeliverable', error);
    }
  },

  async updateDeliverable(id: string, updates: Partial<Omit<Deliverable, 'id' | 'contractId' | 'createdAt'>>): Promise<Deliverable> {
    try {
      return await contractApi.updateDeliverable(id, updates);
    } catch (error) {
      return handleApiError('updateDeliverable', error);
    }
  },

  async getContractDeliverables(contractId: string): Promise<Deliverable[]> {
    try {
      return await contractApi.getContractDeliverables(contractId);
    } catch (error) {
      return handleApiError('getContractDeliverables', error);
    }
  },

  // Utility methods for contract workflow
  async submitWorkAndUpdateStatus(contractId: string, submission: CreateWorkSubmissionDto): Promise<{ workSubmission: WorkSubmission; contract: Contract }> {
    try {
      // Create work submission
      const workSubmission = await this.createWorkSubmission(submission);

      // Update contract status to WORK_SUBMITTED
      const contract = await this.updateContractStatus(contractId, ContractStatus.WORK_SUBMITTED);

      return { workSubmission, contract };
    } catch (error) {
      return handleApiError('submitWorkAndUpdateStatus', error);
    }
  },

  async approveWorkAndComplete(contractId: string, jobId?: string): Promise<Contract> {
    try {
      // Update contract status to COMPLETED
      const contract = await this.updateContractStatus(contractId, ContractStatus.COMPLETED);

      // Update job status to COMPLETED if jobId provided
      if (jobId) {
        await this.updateJobStatus(jobId, JobStatus.COMPLETED);
      }

      return contract;
    } catch (error) {
      return handleApiError('approveWorkAndComplete', error);
    }
  },

  async requestRevisions(contractId: string): Promise<Contract> {
    try {
      // Update contract status to REVISIONS_REQUESTED
      return await this.updateContractStatus(contractId, ContractStatus.REVISIONS_REQUESTED);
    } catch (error) {
      return handleApiError('requestRevisions', error);
    }
  },

  async cancelContract(contractId: string, jobId?: string): Promise<Contract> {
    try {
      // Update contract status to CANCELLED
      const contract = await this.updateContractStatus(contractId, ContractStatus.CANCELLED);

      // Update job status to CANCELLED if jobId provided
      if (jobId) {
        await this.updateJobStatus(jobId, JobStatus.CANCELLED);
      }

      return contract;
    } catch (error) {
      return handleApiError('cancelContract', error);
    }
  },

  async processPayment(contractId: string, amount: number): Promise<{ payment: any; contract: Contract }> {
    try {
      // Create payment record
      const payment = await this.createPayment(contractId, amount);

      // Update contract status to PAID
      const contract = await this.updateContractStatus(contractId, ContractStatus.PAID);

      return { payment, contract };
    } catch (error) {
      return handleApiError('processPayment', error);
    }
  },

};

export default contractService;
