import { gql } from '@apollo/client';

export const CREATE_CONVERSATION = gql`
  mutation CreateConversation($input: CreateConversationInput!) {
    createConversation(input: $input) {
      id
      jobId
      clientId
      freelancerId
      createdAt
      updatedAt
    }
  }
`;

export const  SEND_MESSAGE = gql`
  mutation SendMessage($input: CreateMessageInput!) {
    createMessage(input: $input) {
      id
      messageText
      conversationId
      senderId
      createdAt
      updatedAt
      sender {
        id
        name
        email
        profilePhoto
      }
      conversationData {
        id
        clientId
        freelancerId
        client {
          id
          name
          email
          profilePhoto
        }
        freelancer {
          id
          name
          email
          profilePhoto
        }
      }
    }
  }
`;