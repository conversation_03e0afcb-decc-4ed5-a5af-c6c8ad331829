import React, { useRef, useState, useEffect } from 'react';
import Image from 'next/image';
import useProfilePhoto from '../../../hooks/useProfilePhoto';
import { getProfilePhotoUrl, extractProfilePhotoKey } from '../../../utils/profilePhoto';
import { useToast } from '@/components/ui/toast';
import './ProfilePhotoUploader.css';

interface ProfilePhotoUploaderProps {
  userId: string;
  currentPhotoUrl?: string | null;
  onPhotoUpdate?: (photoUrl: string) => void;
  size?: number;
}

const ProfilePhotoUploader: React.FC<ProfilePhotoUploaderProps> = ({
  userId,
  currentPhotoUrl,
  onPhotoUpdate,
  size = 120,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const { uploadProfilePhoto, deleteProfilePhoto, isUploading, uploadProgress } = useProfilePhoto();
  const { showToast } = useToast();

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const currentPhoto = currentPhotoUrl && currentPhotoUrl !== '/user-avatar.svg' 
        ? currentPhotoUrl 
        : undefined;
        
      const photoUrl = await uploadProfilePhoto(file, userId, currentPhoto);
      
      if (photoUrl && onPhotoUpdate) {
        onPhotoUpdate(photoUrl);
        showToast('Success', {
          description: 'Profile photo updated successfully',
          variant: 'success'
        });
      }
    } catch (error) {
      console.error('Error handling file upload:', error);
      showToast('Error', {
        description: 'Failed to upload profile photo. Please try again.',
        variant: 'destructive'
      });
    } finally {
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDeletePhoto = async () => {
    if (!currentPhotoUrl) return;

    try {
      const key = extractProfilePhotoKey(currentPhotoUrl) || currentPhotoUrl;
      if (key) {
        await deleteProfilePhoto(key, userId);
        if (onPhotoUpdate) {
          onPhotoUpdate('');
          showToast('Success', {
            description: 'Profile photo removed successfully',
            variant: 'success'
          });
        }
      }
    } catch (error) {
      console.error('Error deleting profile photo:', error);
      showToast('Error', {
        description: 'Failed to remove profile photo. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const [photoUrl, setPhotoUrl] = useState('/user-avatar.svg');

  useEffect(() => {
    console.log('ProfilePhotoUploader - currentPhotoUrl:', currentPhotoUrl);
    if (currentPhotoUrl) {
      if (currentPhotoUrl.startsWith('http')) {
        console.log('Using provided URL directly');
        setPhotoUrl(currentPhotoUrl);
      } else {
        console.log('Constructing URL for filename:', currentPhotoUrl);
        const fullUrl = getProfilePhotoUrl(currentPhotoUrl);
        console.log('Constructed URL:', fullUrl);
        if (fullUrl) {
          setPhotoUrl(fullUrl);
        } else {
          console.warn('Failed to construct URL for:', currentPhotoUrl);
        }
      }
    } else {
      console.log('No photo URL provided, using default avatar');
      setPhotoUrl('/user-avatar.svg');
    }
  }, [currentPhotoUrl]);

  return (
    <div 
      className="profile-photo-uploader"
      style={{ width: `${size}px` }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="avatar-container" style={{ width: `${size}px`, height: `${size}px` }}>
        <Image
          src={photoUrl}
          alt="Profile"
          width={size}
          height={size}
          className={`avatar ${isHovered ? 'avatar-hover' : ''}`}
          onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
            (e.target as HTMLImageElement).src = '/user-avatar.svg';
          }}
        />
        
        {isUploading && (
          <span className="upload-text">
            {isUploading 
              ? `Uploading... ${uploadProgress}%` 
              : (currentPhotoUrl ? 'Change Photo' : 'Add Photo')}
          </span>
        )}
        {isUploading && (
          <div className="upload-overlay">
            <div className="progress-container">
              <div 
                className="progress-circle"
                style={{
                  background: `conic-gradient(#4CAF50 ${uploadProgress}%, transparent ${uploadProgress}% 100%)`,
                }}
              >
                <div className="progress-text">{uploadProgress}%</div>
              </div>
            </div>
          </div>
        )}

        <label className={`upload-button ${isHovered || isUploading ? 'visible' : ''}`}>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">
            {currentPhotoUrl ? (
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            ) : (
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            )}
          </svg>
          <span className="button-text">
            {isUploading ? 'Uploading...' : (currentPhotoUrl ? 'Change Photo' : 'Add Photo')}
          </span>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            ref={fileInputRef}
            disabled={isUploading}
            className="visually-hidden"
          />
        </label>
      </div>

      {currentPhotoUrl && (
        <button
          className="remove-button"
          onClick={handleDeletePhoto}
          disabled={isUploading}
        >
          Remove Photo
        </button>
      )}

      {/* Error messages are now shown via toast notifications */}
    </div>
  );
};

export default ProfilePhotoUploader;
