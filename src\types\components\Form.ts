import { FormHTMLAttributes, LabelHT<PERSON>Attributes, ReactNode } from 'react';

export interface FormProps extends FormHTMLAttributes<HTMLFormElement> {
  /** Form content */
  children: ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Callback when form is submitted */
  onSubmit?: (event: React.FormEvent<HTMLFormElement>) => void;
}

export interface FormFieldProps {
  /** Field label */
  label?: string;
  /** Error message */
  error?: string;
  /** Helper text */
  hint?: string;
  /** Whether the field is required */
  required?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Field content */
  children: ReactNode;
  /** ID of the form control the label is bound to */
  htmlFor?: string;
  /** Additional label props */
  labelProps?: LabelHTMLAttributes<HTMLLabelElement>;
}

export interface LabelProps extends LabelHTMLAttributes<HTMLLabelElement> {
  /** Whether the field is required */
  required?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Label content */
  children: ReactNode;
}
