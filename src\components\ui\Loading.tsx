import React from 'react';
import { cn } from '@/lib/utils';

export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse';
  className?: string;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
};

const Spinner = ({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg'; className?: string }) => (
  <div
    className={cn(
      'animate-spin rounded-full border-2 border-current border-t-transparent',
      sizeClasses[size],
      className
    )}
    role="status"
    aria-label="Loading"
  >
    <span className="sr-only">Loading...</span>
  </div>
);

const Dots = ({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg'; className?: string }) => {
  const dotSize = size === 'sm' ? 'h-1 w-1' : size === 'md' ? 'h-1.5 w-1.5' : 'h-2 w-2';
  
  return (
    <div className={cn('flex space-x-1', className)} role="status" aria-label="Loading">
      <div className={cn('animate-bounce rounded-full bg-current', dotSize)} style={{ animationDelay: '0ms' }} />
      <div className={cn('animate-bounce rounded-full bg-current', dotSize)} style={{ animationDelay: '150ms' }} />
      <div className={cn('animate-bounce rounded-full bg-current', dotSize)} style={{ animationDelay: '300ms' }} />
      <span className="sr-only">Loading...</span>
    </div>
  );
};

const Pulse = ({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg'; className?: string }) => (
  <div
    className={cn(
      'animate-pulse rounded-full bg-current opacity-75',
      sizeClasses[size],
      className
    )}
    role="status"
    aria-label="Loading"
  >
    <span className="sr-only">Loading...</span>
  </div>
);

const Loading: React.FC<LoadingProps> = ({ size = 'md', variant = 'spinner', className }) => {
  switch (variant) {
    case 'dots':
      return <Dots size={size} className={className} />;
    case 'pulse':
      return <Pulse size={size} className={className} />;
    case 'spinner':
    default:
      return <Spinner size={size} className={className} />;
  }
};

export interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  loadingText?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  children,
  className,
  loadingText = 'Loading...',
}) => (
  <div className={cn('relative', className)}>
    {children}
    {isLoading && (
      <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col items-center space-y-2">
          <Loading size="lg" />
          <p className="text-sm text-muted-foreground">{loadingText}</p>
        </div>
      </div>
    )}
  </div>
);

export { Loading, LoadingOverlay, Spinner, Dots, Pulse };
