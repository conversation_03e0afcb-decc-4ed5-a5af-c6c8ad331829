"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import type { Job, JobWithProposalList } from "@/types/job";
import type { JobProposal } from "@/types/proposal";
import { ProposalStatus } from "@/types/proposal.types";
import { jobService } from '@/api/jobs/job.service';
import { useMessaging } from "@/hooks/useMessaging";
import { formatDistanceToNow } from "date-fns";
import { toast } from "react-hot-toast";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { useContractCheck, shouldShowCreateContractButton, getCreateContractUrl } from '@/hooks/useContractCheck';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";

type StatusFilter = "" | ProposalStatus;

// Component to handle contract creation button for individual proposals
const ProposalContractButton: React.FC<{ proposal: JobProposal }> = ({ proposal }) => {
  const { hasExistingContract, isLoading } = useContractCheck(proposal.id);

  if (isLoading) {
    return (
      <Button size="sm" disabled>
        <Icon name="Loader2" size="sm" className="mr-2 animate-spin" />
        Checking...
      </Button>
    );
  }

  if (!shouldShowCreateContractButton(proposal.status, hasExistingContract)) {
    return null;
  }

  return (
    <Button size="sm" asChild>
      <Link href={getCreateContractUrl(proposal.jobId, proposal.id)}>
        <Icon name="FileText" size="sm" className="mr-2" />
        Create Contract
      </Link>
    </Button>
  );
};

const JobProposalsPage = () => {
  const { id } = useParams<{ id: string }>();
  const {
    isAuthenticated,
    user,
    loading: authLoading,
    cognitoUserId,
    isInitialized,
  } = useAuth();
  const router = useRouter();
  const [job, setJob] = useState<Job | JobWithProposalList | null>(null);
  const [proposals, setProposals] = useState<JobProposal[]>([]);
  const [filteredProposals, setFilteredProposals] = useState<JobProposal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("");

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [authLoading, isAuthenticated, router]);

  const fetchProposals = useCallback(async () => {
    const userIdentifier = cognitoUserId;
    if (!id || !userIdentifier) return;

    try {
      setIsLoading(true);
      const jobData = await jobService.getJob(id);

      if (jobData.clientId !== userIdentifier) {
        router.push("/client/jobs");
        return;
      }

      const typedJobData = jobData as JobWithProposalList;
      setJob(typedJobData);

      let jobProposals: JobProposal[] = [];

      if (Array.isArray(typedJobData.proposals)) {
        jobProposals = typedJobData.proposals;
      } else if (
        typedJobData.proposals &&
        typeof typedJobData.proposals === "object" &&
        "items" in typedJobData.proposals
      ) {
        const proposalsWithItems = typedJobData.proposals as {
          items: JobProposal[];
        };
        jobProposals = Array.isArray(proposalsWithItems.items)
          ? proposalsWithItems.items
          : [];
      }

      setProposals(jobProposals);
      setFilteredProposals([...jobProposals]);
      setError(null);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError("Failed to load job proposals. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [id, cognitoUserId, router]);

  useEffect(() => {
    if (!proposals.length) {
      setFilteredProposals([]);
      return;
    }

    const filtered = proposals.filter((proposal) => {
      const searchTermLower = searchTerm.toLowerCase();
      const matchesSearch =
        !searchTerm ||
        proposal.freelancer?.name?.toLowerCase().includes(searchTermLower) ||
        proposal.coverLetter?.toLowerCase().includes(searchTermLower) ||
        proposal.freelancerId?.toLowerCase().includes(searchTermLower);

      const matchesStatus = !statusFilter || proposal.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    setFilteredProposals(filtered);
  }, [proposals, searchTerm, statusFilter]);

  useEffect(() => {
    const userRole = user?.attributes?.["custom:role"] || "CLIENT";

    const shouldCallFetchProposals =
      isAuthenticated &&
      userRole === "CLIENT" &&
      !authLoading &&
      isInitialized &&
      Boolean(cognitoUserId);

    if (shouldCallFetchProposals) {
      fetchProposals();
    }
  }, [
    isAuthenticated,
    user,
    fetchProposals,
    authLoading,
    isInitialized,
    cognitoUserId,
  ]);

  const { startThreadFromApplication } = useMessaging();
  const [messagingLoading, setMessagingLoading] = useState<string | null>(null);

  const handleMessageApplicant = async (proposal: JobProposal) => {
    if (!job || !proposal.freelancerId) return;
    
    try {
      setMessagingLoading(proposal.id);
      await startThreadFromApplication(
        job.id,
        job.clientId,
        proposal.freelancerId,
      );
      toast.success('Messaging thread created');
    } catch (error) {
      console.error('Error creating message thread:', error);
      toast.error('Failed to start conversation');
    } finally {
      setMessagingLoading(null);
    }
  };

  const handleUpdateStatus = async (
    proposalId: string,
    status: ProposalStatus
  ) => {
    if (!proposalId || !status || !job?.id) return;

    const confirmed = window.confirm(
      `Are you sure you want to ${status.toLowerCase()} this proposal?`
    );

    if (!confirmed) return;

    try {
      setIsUpdating(true);

      const updatedProposal = await jobService.updateProposalStatus(
        proposalId,
        status
      );

      if (status === "ACCEPTED" && job) {
        try {
          await jobService.updateJob({
            id: job.id,
            status: "IN_PROGRESS",
          });

          const proposal = proposals.find(p => p.id === proposalId);
          if (proposal?.freelancerId) {
            await startThreadFromApplication(
              job.id,
              job.clientId,
              proposal.freelancerId,
            );
          }

          setJob((prev) => (prev ? { ...prev, status: "IN_PROGRESS" } : null));

          setProposals((prev) =>
            prev.map((p) =>
              p.id === proposalId
                ? {
                    ...p,
                    ...updatedProposal,
                    updatedAt: new Date().toISOString(),
                  }
                : p.status === "PENDING"
                ? {
                    ...p,
                    status: "REJECTED",
                    updatedAt: new Date().toISOString(),
                  }
                : p
            )
          );

          toast.success("Proposal accepted and job marked as in progress");
          return;
        } catch (jobError) {
          console.error("Error updating job status:", jobError);
          toast.error("Proposal accepted but failed to update job status");
          return;
        }
      }

      if (status === ProposalStatus.ACCEPTED) {
        const otherProposals = proposals.filter((p) => p.id !== proposalId);
        await Promise.all(
          otherProposals.map((p) =>
            jobService.updateProposalStatus(p.id, ProposalStatus.REJECTED)
          )
        );
      }

      setProposals((prev) =>
        prev.map((proposal) =>
          proposal.id === proposalId
            ? {
                ...proposal,
                ...updatedProposal,
                updatedAt: new Date().toISOString(),
              }
            : proposal
        )
      );

      toast.success(`Proposal ${status.toLowerCase()} successfully`);
    } catch (err) {
      console.error("Error updating proposal status:", err);
      toast.error("Failed to update proposal status");
    } finally {
      setIsUpdating(false);
    }
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <Icon name="XCircle" className="h-6 w-6 text-red-600" />
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">
          Error loading proposals
        </h3>
        <p className="mt-1 text-sm text-gray-500">{error}</p>
        <div className="mt-6">
          <Button onClick={() => fetchProposals()}>
            <Icon name="RefreshCw" className="mr-2 h-4 w-4" />
            Try again
          </Button>
          <Icon name="XCircle" size="lg" className="text-red-600" />
        </div>
        <h3 className="mt-3 text-lg font-medium text-gray-900">Error</h3>
        <p className="mt-2 text-sm text-gray-500">{error}</p>
        <div className="mt-4">
          <button
            onClick={() => router.push("/client/jobs")}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer"
          >
            Back
          </button>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="text-center py-12">
        <Icon name="Briefcase" className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">
          Job not found
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          The requested job could not be found.
        </p>
        <Button onClick={() => router.push("/client/jobs")} className="mt-4">
          Back
        </Button>
      </div>
    );
  }

  const hasAcceptedProposal = proposals.some(
    (proposal) => proposal.status === ProposalStatus.ACCEPTED
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <ContentHeader
        title="Job Proposals"
        subtitle={`Viewing proposals for ${job.title}`}
        breadcrumbs={[
          { label: "Jobs", href: "/client/jobs" },
          { label: job.title, href: `/client/jobs/${id}` },
          { label: "Proposals", href: "#" },
        ]}
      />

      <div className="mt-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="w-full md:w-64">
            <div className="relative">
              <Icon
                name="Search"
                className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"
              />
              <Input
                type="search"
                placeholder="Search proposals..."
                className="w-full bg-background pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="w-full md:w-48">
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as StatusFilter)}
              options={[
                { value: "", label: "All Statuses" },
                { value: ProposalStatus.PENDING, label: "Pending" },
                { value: ProposalStatus.ACCEPTED, label: "Accepted" },
                { value: ProposalStatus.REJECTED, label: "Rejected" },
              ]}
              placeholder="Filter by status"
            />
          </div>
        </div>

        {filteredProposals.length === 0 ? (
          <div className="text-center py-12">
            <Icon
              name="Briefcase"
              className="mx-auto h-12 w-12 text-gray-400"
            />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {searchTerm || statusFilter
                ? "No matching proposals found"
                : "No proposals yet"}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || statusFilter
                ? "Try adjusting your search or filter criteria"
                : "No one has submitted a proposal for this job yet. Check back later."}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredProposals.map((proposal) => (
              <Card key={proposal.id} className="overflow-hidden">
                <CardHeader className="flex flex-row items-start justify-between pb-2">
                  <div>
                    <CardTitle className="text-lg">
                      {proposal?.freelancer?.name || "Anonymous Freelancer"}
                    </CardTitle>
                    <div className="mt-2">
                      <p className="text-sm text-muted-foreground">
                        Submitted{" "}
                        {formatDistanceToNow(new Date(proposal.createdAt), {
                          addSuffix: true,
                        })}
                      </p>
                    </div>
                  </div>

                  {proposal.bidAmount > 0 && (
                    <div className="text-right">
                      <p className="text-sm font-medium text-muted-foreground">
                        Bid Amount
                      </p>
                      <p className="text-lg font-semibold">
                        ${proposal.bidAmount.toFixed(2)}
                      </p>
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMessageApplicant(proposal);
                      }}
                      disabled={messagingLoading === proposal.id}
                      className="h-8 px-3 text-xs border-primary/30 hover:bg-primary/5 transition-colors"
                    >
                      {messagingLoading === proposal.id ? (
                        <Icon name="Loader2" className="h-3.5 w-3.5 animate-spin mr-1.5" />
                      ) : (
                        <>
                          <Icon name="MessageSquare" className="mr-1.5 h-3.5 w-3.5 text-primary" />
                          <span className="font-medium">Message</span>
                        </>
                      )}
                    </Button>
                  </div>
                </CardHeader>

                <CardContent className="pt-4">
                  <div className="space-y-4">
                    {proposal.coverLetter && (
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground mb-2">
                          Cover Letter
                        </h4>
                        <p className="text-sm whitespace-pre-line">
                          {proposal.coverLetter}
                        </p>
                      </div>
                    )}

                    <div className="flex flex-wrap items-center justify-between gap-2 pt-2">
                      {proposal.status === ProposalStatus.PENDING && (
                        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                          Pending
                        </Badge>
                      )}
                      {proposal.status === ProposalStatus.ACCEPTED && (
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                            Accepted
                          </Badge>
                          <ProposalContractButton proposal={proposal} />
                        </div>
                      )}
                      {proposal.status === ProposalStatus.REJECTED && (
                        <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                          Rejected
                        </Badge>
                      )}

                      {proposal.status === ProposalStatus.PENDING && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleUpdateStatus(proposal.id, ProposalStatus.ACCEPTED)
                            }
                            disabled={isUpdating || hasAcceptedProposal}
                          >
                            {isUpdating ? "Updating..." : "Accept"}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-destructive hover:text-destructive"
                            onClick={() =>
                              handleUpdateStatus(proposal.id, ProposalStatus.REJECTED)
                            }
                            disabled={isUpdating}
                          >
                            {isUpdating ? "Updating..." : "Reject"}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default JobProposalsPage;
