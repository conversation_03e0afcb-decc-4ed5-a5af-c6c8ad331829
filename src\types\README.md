# TypeScript Types Organization

This directory contains all TypeScript type definitions for the application, organized in a scalable and maintainable structure.

## Directory Structure

```
types/
├── common/                 # Common types and utilities used across the application
│   ├── api.types.ts       # API-related types (responses, pagination, etc.)
│   └── index.ts           # Common type exports
│
├── features/              # Feature-specific types
│   ├── auth/              # Authentication and user-related types
│   ├── jobs/              # Job-related types
│   ├── proposals/         # Proposal-related types
│   ├── messaging/         # Messaging and chat types
│   └── index.ts           # Feature type exports
│
├── api/                   # API client and related types
│   └── api.types.ts       # API client types and interfaces
│
└── index.ts               # Main entry point for all types
```

## Type Naming Conventions

- **Interfaces**: Use `PascalCase` without `I` prefix (e.g., `UserProfile`, `JobDetails`)
- **Props**: Suffix with `Props` (e.g., `ButtonProps`, `UserCardProps`)
- **DTOs**: Suffix with `Dto` (e.g., `CreateUserDto`, `UpdateProfileDto`)
- **Enums**: Use `PascalCase` (e.g., `UserRole`, `ProposalStatus`)
- **Type Aliases**: Use `PascalCase` (e.g., `ApiResponse<T>`, `PaginatedResult<T>`)

## Usage Examples

### Importing Types

```typescript
import { UserProfile, Job, ProposalStatus } from '@/types';

import * as JobTypes from '@/types/features/jobs/job.types';

import { JobTypes, AuthTypes } from '@/types';
```

### Creating New Types

1. **Common Types**: Add to the appropriate file in `types/common/`
2. **Feature Types**: Add to the corresponding feature directory under `types/features/`
3. **API Types**: Add API-specific types to `types/api/`

## Best Practices

1. **Avoid `any`**: Always use proper types or `unknown` when the type is truly dynamic
2. **Reuse Types**: Leverage existing types through composition and extension
3. **Document Complex Types**: Add JSDoc comments for complex types
4. **Keep Imports Clean**: Use the barrel files (`index.ts`) for clean imports
5. **Type Guards**: Use type guards for runtime type checking when needed

## Migration from Legacy Types

Legacy types are still available but should be migrated to the new structure. When updating components:

1. Update imports to use the new type locations
2. Replace any `any` types with proper type definitions
3. Move component-specific types to the feature directory

## Type Generation

For GraphQL types, consider using `graphql-codegen` to automatically generate TypeScript types from your schema and operations.

## Testing Types

Consider using `@ts-expect-error` and `@ts-check` comments to test type boundaries and catch potential issues early.
