'use client';

import { useEffect, useRef, useState } from 'react';
import { staggerDelay } from '@/utils/animations';

interface AnimateOnScrollProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
  animation?: 'fadeIn' | 'slideUp' | 'fadeInUp';
  threshold?: number;
}

export function AnimateOnScroll({
  children,
  delay = 0,
  className = '',
  animation = 'fadeIn',
  threshold = 0.1,
}: AnimateOnScrollProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const node = ref.current;
    if (!node) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold }
    );

    observer.observe(node);

    return () => {
      observer.unobserve(node);
    };
  }, [threshold]);

  const animationClasses = {
    fadeIn: 'opacity-0 animate-fade-in',
    slideUp: 'opacity-0 animate-slide-up',
    fadeInUp: 'opacity-0 animate-fade-in-up',
  };

  return (
    <div
      ref={ref}
      className={`transition-all duration-500 ${isVisible ? 'opacity-100' : animationClasses[animation]} ${className}`}
      style={isVisible ? undefined : { ...staggerDelay(delay / 100) }}
    >
      {children}
    </div>
  );
}

export function AnimateList<T = unknown>({
  items,
  renderItem,
  className = '',
  itemClassName = '',
  animation = 'fadeInUp',
  delayStep = 100,
}: {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  itemClassName?: string;
  animation?: 'fadeIn' | 'slideUp' | 'fadeInUp';
  delayStep?: number;
}) {
  return (
    <div className={className}>
      {items.map((item, index) => (
        <AnimateOnScroll
          key={index}
          delay={index * delayStep}
          className={itemClassName}
          animation={animation}
        >
          {renderItem(item, index)}
        </AnimateOnScroll>
      ))}
    </div>
  );
}
