export type ApiResponse<T> = 
  | { status: 'success'; data: T }
  | { status: 'error'; error: ApiError };

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

export type PaginatedResponse<T> = {
  items: T[];
  nextToken?: string;
  total?: number;
};

export type SortDirection = 'asc' | 'desc';

export interface SortOption {
  field: string;
  direction: SortDirection;
}

export interface PaginationParams {
  limit?: number;
  nextToken?: string;
  sort?: SortOption[];
}

export type Nullable<T> = T | null;
export type Maybe<T> = T | undefined;
export type Dictionary<T> = Record<string, T>;
export type ValueOf<T> = T[keyof T];
