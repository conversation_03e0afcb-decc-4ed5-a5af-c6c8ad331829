import { graphQLClient } from '@/lib/graphql/graphqlClient';
import {
  GET_CONTRACT,
  LIST_CONTRACTS,
  GET_CONTRACTS_BY_JOB,
  GET_USER_CONTRACTS,
  GET_CONTRACT_WORK_SUBMISSIONS,
  GET_CONTRACT_DELIVERABLES,
  GET_CONTRACT_PAYMENT_SCHEDULES,
  GET_CONTRACT_PAYMENTS
} from './contract.queries';
import {
  CREATE_CONTRACT,
  UPDATE_CONTRACT,
  UPDATE_CONTRACT_STATUS,
  ACCEPT_CONTRACT,
  REJECT_CONTRACT,
  COMPLETE_CONTRACT,
  CREATE_PAYMENT_SCHEDULE,
  UPDATE_PAYMENT_SCHEDULE,
  CREATE_DELIVERABLE,
  UPDATE_DELIVERABLE,
  CREATE_WORK_SUBMISSION,
  UPDATE_WORK_SUBMISSION,
  UPDATE_JOB_STATUS,
  CREATE_PAYMENT
} from './contract.mutations';
import type {
  Contract,
  ContractFilters,
  ContractStatus,
  CreateContractDto,
  UpdateContractDto,
  PaymentSchedule,
  Deliverable,
  WorkSubmission,
  CreateWorkSubmissionDto,
  ReviewWorkSubmissionDto,
} from '@/types/features/contracts/contract.types';
import type { JobStatus } from '@/types/features/jobs/job.types';

export const contractApi = {
  createContract: async (input: CreateContractDto) => {
    const response = await graphQLClient.mutate<{ createContract: Contract }>(
      CREATE_CONTRACT,
      { input },
      { authMode: 'userPool' }
    );
    return response.createContract;
  },

  getContract: async (id: string) => {
    const response = await graphQLClient.query<{ getContract: Contract }>(
      GET_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.getContract;
  },

  updateContract: async (input: UpdateContractDto) => {
    const response = await graphQLClient.mutate<{ updateContract: Contract }>(
      UPDATE_CONTRACT,
      { input },
      { authMode: 'userPool' }
    );
    return response.updateContract;
  },

  updateContractStatus: async (id: string, status: ContractStatus) => {
    const response = await graphQLClient.mutate<{ updateContractStatus: Contract }>(
      UPDATE_CONTRACT_STATUS,
      { id, status },
      { authMode: 'userPool' }
    );
    return response.updateContractStatus;
  },

  acceptContract: async (id: string) => {
    const response = await graphQLClient.mutate<{ acceptContract: Contract }>(
      ACCEPT_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.acceptContract;
  },

  rejectContract: async (id: string) => {
    const response = await graphQLClient.mutate<{ rejectContract: Contract }>(
      REJECT_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.rejectContract;
  },

  completeContract: async (id: string) => {
    const response = await graphQLClient.mutate<{ completeContract: Contract }>(
      COMPLETE_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.completeContract;
  },

  listContracts: async (filter?: ContractFilters, limit?: number, nextToken?: string) => {
    const response = await graphQLClient.query<{
      listContracts: {
        items: Contract[];
        nextToken?: string
      }
    }>(
      LIST_CONTRACTS,
      {
        filter: transformFilter(filter),
        limit,
        nextToken
      },
      { authMode: 'userPool' }
    );
    return response.listContracts;
  },

  getContractsByJob: async (jobId: string) => {
    const response = await graphQLClient.query<{ getContractsByJob: Contract[] }>(
      GET_CONTRACTS_BY_JOB,
      { jobId },
      { authMode: 'userPool' }
    );
    return response.getContractsByJob;
  },

  getUserContracts: async (userId: string, status?: ContractStatus) => {
    const response = await graphQLClient.query<{ getUserContracts: Contract[] }>(
      GET_USER_CONTRACTS,
      { userId, status },
      { authMode: 'userPool' }
    );
    return response.getUserContracts;
  },

  createPaymentSchedule: async (contractId: string, payment: Omit<PaymentSchedule, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>) => {
    const response = await graphQLClient.mutate<{ createPaymentSchedule: PaymentSchedule }>(
      CREATE_PAYMENT_SCHEDULE,
      { input: { contractId, ...payment } },
      { authMode: 'userPool' }
    );
    return response.createPaymentSchedule;
  },

  updatePaymentSchedule: async (id: string, updates: Partial<Omit<PaymentSchedule, 'id' | 'contractId' | 'createdAt'>>) => {
    const response = await graphQLClient.mutate<{ updatePaymentSchedule: PaymentSchedule }>(
      UPDATE_PAYMENT_SCHEDULE,
      { id, ...updates },
      { authMode: 'userPool' }
    );
    return response.updatePaymentSchedule;
  },

  createDeliverable: async (contractId: string, deliverable: Omit<Deliverable, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>) => {
    const response = await graphQLClient.mutate<{ createDeliverable: Deliverable }>(
      CREATE_DELIVERABLE,
      { input: { contractId, ...deliverable } },
      { authMode: 'userPool' }
    );
    return response.createDeliverable;
  },

  updateDeliverable: async (id: string, updates: Partial<Omit<Deliverable, 'id' | 'contractId' | 'createdAt'>>) => {
    const response = await graphQLClient.mutate<{ updateDeliverable: Deliverable }>(
      UPDATE_DELIVERABLE,
      { id, ...updates },
      { authMode: 'userPool' }
    );
    return response.updateDeliverable;
  },

  // Work Submission APIs
  createWorkSubmission: async (submission: CreateWorkSubmissionDto) => {
    const response = await graphQLClient.mutate<{ createWorkSubmission: WorkSubmission }>(
      CREATE_WORK_SUBMISSION,
      { input: submission },
      { authMode: 'userPool' }
    );
    return response.createWorkSubmission;
  },

  updateWorkSubmission: async (id: string, updates: Omit<ReviewWorkSubmissionDto, 'id'>) => {
    const response = await graphQLClient.mutate<{ updateWorkSubmission: WorkSubmission }>(
      UPDATE_WORK_SUBMISSION,
      { input: { id, ...updates } },
      { authMode: 'userPool' }
    );
    return response.updateWorkSubmission;
  },

  getContractWorkSubmissions: async (contractId: string) => {
    const response = await graphQLClient.query<{ listWorkSubmissions: { items: WorkSubmission[] } }>(
      GET_CONTRACT_WORK_SUBMISSIONS,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listWorkSubmissions.items;
  },

  // Job Status Update
  updateJobStatus: async (jobId: string, status: JobStatus) => {
    const response = await graphQLClient.mutate<{ updateJob: { id: string; status: JobStatus } }>(
      UPDATE_JOB_STATUS,
      { id: jobId, status },
      { authMode: 'userPool' }
    );
    return response.updateJob;
  },

  // Payment APIs
  createPayment: async (contractId: string, amount: number, method: string = 'STRIPE') => {
    const response = await graphQLClient.mutate<{ createPayment: any }>(
      CREATE_PAYMENT,
      {
        input: {
          contractId,
          amount,
          status: 'PAID',
          method,
          paidAt: new Date().toISOString()
        }
      },
      { authMode: 'userPool' }
    );
    return response.createPayment;
  },

  getContractPayments: async (contractId: string) => {
    const response = await graphQLClient.query<{ listPayments: { items: any[] } }>(
      GET_CONTRACT_PAYMENTS,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listPayments.items;
  },

  // Additional query functions
  getContractDeliverables: async (contractId: string) => {
    const response = await graphQLClient.query<{ listDeliverables: { items: Deliverable[] } }>(
      GET_CONTRACT_DELIVERABLES,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listDeliverables.items;
  },

  getContractPaymentSchedules: async (contractId: string) => {
    const response = await graphQLClient.query<{ listPaymentSchedules: { items: PaymentSchedule[] } }>(
      GET_CONTRACT_PAYMENT_SCHEDULES,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listPaymentSchedules.items;
  }
};

function transformFilter(filter?: ContractFilters): any {
  if (!filter) return undefined;

  const conditions: any[] = [];

  if (filter.status) {
    conditions.push({ status: { eq: filter.status } });
  }

  if (filter.clientId) {
    conditions.push({ clientId: { eq: filter.clientId } });
  }

  if (filter.freelancerId) {
    conditions.push({ freelancerId: { eq: filter.freelancerId } });
  }

  if (filter.jobId) {
    conditions.push({ jobId: { eq: filter.jobId } });
  }

  if (filter.proposalId) {
    conditions.push({ proposalId: { eq: filter.proposalId } });
  }

  if (filter.startDateFrom || filter.startDateTo) {
    const dateFilter: any = {};
    if (filter.startDateFrom) dateFilter.ge = filter.startDateFrom;
    if (filter.startDateTo) dateFilter.le = filter.startDateTo;
    conditions.push({ createdAt: dateFilter });
  }

  if (conditions.length === 0) {
    return undefined;
  }

  if (conditions.length === 1) {
    const condition = conditions[0];
    return condition;
  }

  return { and: conditions };
}
