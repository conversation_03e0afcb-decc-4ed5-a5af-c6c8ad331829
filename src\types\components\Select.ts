import { SelectHTMLAttributes } from 'react';

export interface SelectOption {
  /** Value of the option */
  value: string;
  /** Display text for the option */
  label: string;
  /** Whether the option is disabled */
  disabled?: boolean;
}

export interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement> {
  /** Array of options to display in the select */
  options: SelectOption[];
  /** Placeholder text when no option is selected */
  placeholder?: string;
  /** Whether the select has an error */
  error?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Whether the select is in a loading state */
  isLoading?: boolean;
}
