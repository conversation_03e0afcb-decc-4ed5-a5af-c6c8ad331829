'use client';

import { useEffect, useState, useCallback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from "@/lib/auth/AuthContext";
import { MessagingPage } from '@/components/messaging/MessagingPage';
import { UserRole } from '@/types/enums';
import messageService, { 
  User as ServiceUser
} from '@/api/messaging/message.service';
import { MessagingUser, Conversation } from '@/types/messaging';

import { toast } from 'react-hot-toast';

const toUIUser = (user: ServiceUser, role: UserRole): MessagingUser => ({
  id: user.id,
  name: user.name,
  email: user.email,
  avatar: user.avatar || '/default-avatar.png',
  role: (role === UserRole.CLIENT || role === UserRole.FREELANCER ? role : UserRole.CLIENT) as 'CLIENT' | 'FREELANCER',
  isOnline: false
});

export default function ClientMessagesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, cognitoUserId } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);

  useEffect(() => {
    console.log('Conversations updated:', {
      count: conversations.length,
      conversations: conversations.map(c => {
        const lastMessagePreview = c.lastMessage?.content 
          ? c.lastMessage.content.substring(0, 30) + (c.lastMessage.content.length > 30 ? '...' : '')
          : 'No message';
          
        return {
          id: c.id,
          client: c.client?.name,
          freelancer: c.freelancer?.name,
          lastMessage: lastMessagePreview
        };
      })
    });
  }, [conversations]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(
    searchParams.get('conversationId')
  );

  const currentUser = useMemo<MessagingUser>(() => {
    const avatar = user?.attributes?.avatar;
    const avatarUrl = Array.isArray(avatar) ? avatar[0] : avatar;
    
    return {
      id: cognitoUserId || '',
      name: user?.attributes?.name || user?.attributes?.email?.split('@')[0] || 'User',
      email: user?.attributes?.email || '',
      avatar: avatarUrl || '/default-avatar.png',
      role: 'CLIENT',
      isOnline: true
    };
  }, [cognitoUserId, user]);

  const messagingConversations = useMemo<Conversation[]>(() => {
    return conversations.map(conv => ({
      ...conv,
      id: conv.id,
      jobId: conv.jobId || '',
      clientId: conv.clientId,
      freelancerId: conv.freelancerId,
      createdAt: conv.createdAt || new Date().toISOString(),
      updatedAt: conv.updatedAt || new Date().toISOString(),
      messages: conv.messages || [],
      participants: [
        conv.client ? toUIUser(conv.client, UserRole.CLIENT) : {
          id: conv.clientId,
          name: 'Client',
          email: '',
          role: UserRole.CLIENT as 'CLIENT',
          isOnline: false,
          avatar: '/default-avatar.png'
        } as MessagingUser,
        conv.freelancer ? toUIUser(conv.freelancer, UserRole.FREELANCER) : {
          id: conv.freelancerId,
          name: 'Freelancer',
          email: '',
          role: UserRole.FREELANCER as 'FREELANCER',
          isOnline: false,
          avatar: '/default-avatar.png'
        } as MessagingUser
      ],
      client: conv.client ? toUIUser(conv.client, UserRole.CLIENT) : {
        id: conv.clientId,
        name: 'Client',
        email: '',
        role: UserRole.CLIENT as 'CLIENT',
        isOnline: false,
        avatar: '/default-avatar.png'
      } as MessagingUser,
      freelancer: conv.freelancer ? toUIUser(conv.freelancer, UserRole.FREELANCER) : {
        id: conv.freelancerId,
        name: 'Freelancer',
        email: '',
        role: UserRole.FREELANCER as 'FREELANCER',
        isOnline: false,
        avatar: '/default-avatar.png'
      } as MessagingUser,
      job: conv.job || { id: '', title: 'Untitled Job' },
      lastMessage: conv.lastMessage || (conv.messages?.[0] ? {
        id: conv.messages[0].id,
        content: conv.messages[0].messageText,
        senderId: conv.messages[0].senderId,
        conversationId: conv.messages[0].conversationId,
        createdAt: conv.messages[0].createdAt,
        status: 'delivered' as const,
        type: (conv.messages[0].type as 'text' | 'file' | 'image') || 'text'
      } : undefined),
      unreadCount: conv.unreadCount || 0
    }));
  }, [conversations]);

  const selectedConversation = useMemo(() => {
    if (!selectedConversationId) return null;
    return messagingConversations.find(conv => conv.id === selectedConversationId) || null;
  }, [messagingConversations, selectedConversationId]);

  const fetchConversations = useCallback(async () => {
    if (!cognitoUserId) return;
    
    try {
      const data = await messageService.listMyConversations(cognitoUserId);
      const transformed: Conversation[] = data.map(conv => {
        const lastMessage = conv.messages?.[0] ? {
          id: conv.messages[0].id,
          content: conv.messages[0].messageText || '',
          senderId: conv.messages[0].senderId,
          conversationId: conv.messages[0].conversationId || conv.id,
          createdAt: conv.messages[0].createdAt,
          updatedAt: conv.messages[0].updatedAt || conv.messages[0].createdAt,
          status: 'delivered' as const,
          type: (conv.messages[0].type as 'text' | 'file' | 'image') || 'text',
          sender: conv.client?.id === conv.messages[0].senderId ? {
            id: conv.client.id,
            name: conv.client.name,
            email: conv.client.email || '',
            role: 'CLIENT',
            isOnline: false,
            avatar: conv.client.avatar
          } : {
            id: conv.freelancer?.id || '',
            name: conv.freelancer?.name || 'Freelancer',
            email: conv.freelancer?.email || '',
            role: 'FREELANCER',
            isOnline: false,
            avatar: conv.freelancer?.avatar
          },
          receiver: conv.client?.id === conv.messages[0].senderId ? {
            id: conv.freelancer?.id || '',
            name: conv.freelancer?.name || 'Freelancer',
            email: conv.freelancer?.email || '',
            role: 'FREELANCER',
            isOnline: false,
            avatar: conv.freelancer?.avatar
          } : {
            id: conv.client?.id || '',
            name: conv.client?.name || 'Client',
            email: conv.client?.email || '',
            role: 'CLIENT',
            isOnline: false,
            avatar: conv.client?.avatar
          }
        } : undefined;

        return {
          ...conv,
          id: conv.id,
          jobId: conv.jobId || '',
          clientId: conv.clientId,
          freelancerId: conv.freelancerId,
          createdAt: conv.createdAt || new Date().toISOString(),
          updatedAt: conv.updatedAt || new Date().toISOString(),
          messages: conv.messages ? conv.messages.map(m => ({
            ...m,
            content: m.messageText || '',
            receiverId: m.receiverId || '',
            conversationId: m.conversationId || conv.id,
            updatedAt: m.updatedAt || m.createdAt,
            status: 'delivered',
            type: (m.type as 'text' | 'file' | 'image') || 'text',
            sender: conv.client?.id === m.senderId ? {
              id: conv.client.id,
              name: conv.client.name,
              email: conv.client.email || '',
              role: 'CLIENT',
              isOnline: false,
              avatar: conv.client.avatar
            } : {
              id: conv.freelancer?.id || '',
              name: conv.freelancer?.name || 'Freelancer',
              email: conv.freelancer?.email || '',
              role: 'FREELANCER',
              isOnline: false,
              avatar: conv.freelancer?.avatar
            },
            receiver: conv.client?.id === m.senderId ? {
              id: conv.freelancer?.id || '',
              name: conv.freelancer?.name || 'Freelancer',
              email: conv.freelancer?.email || '',
              role: 'FREELANCER',
              isOnline: false,
              avatar: conv.freelancer?.avatar
            } : {
              id: conv.client?.id || '',
              name: conv.client?.name || 'Client',
              email: conv.client?.email || '',
              role: 'CLIENT',
              isOnline: false,
              avatar: conv.client?.avatar
            }
          })) : [],
          participants: [
            {
              id: conv.clientId,
              name: conv.client?.name || 'Client',
              email: conv.client?.email || '',
              role: 'CLIENT' as const,
              isOnline: false,
              avatar: conv.client?.avatar
            },
            {
              id: conv.freelancerId,
              name: conv.freelancer?.name || 'Freelancer',
              email: conv.freelancer?.email || '',
              role: 'FREELANCER' as const,
              isOnline: false,
              avatar: conv.freelancer?.avatar
            }
          ],
          client: {
            id: conv.clientId,
            name: conv.client?.name || 'Client',
            email: conv.client?.email || '',
            role: 'CLIENT' as const,
            isOnline: false,
            avatar: conv.client?.avatar
          },
          freelancer: {
            id: conv.freelancerId,
            name: conv.freelancer?.name || 'Freelancer',
            email: conv.freelancer?.email || '',
            role: 'FREELANCER' as const,
            isOnline: false,
            avatar: conv.freelancer?.avatar
          },
          job: conv.job || { id: '', title: 'Untitled Job' },
          lastMessage,
          unreadCount: conv.unreadCount || 0
        };
      });
      setConversations(transformed);
    } catch (error) {
      console.error('Failed to fetch conversations:', error);
      toast.error('Failed to load conversations');
    } finally {
      setIsLoading(false);
    }
  }, [cognitoUserId]);

  const initializeConversation = useCallback(async () => {
    const toUserId = searchParams.get('to');
    const jobId = searchParams.get('jobId');
    const conversationIdParam = searchParams.get('conversationId');
    
    if (!cognitoUserId) return;
    
    if (conversationIdParam) {
      const conversationExists = conversations.some(conv => conv.id === conversationIdParam);
      if (conversationExists) {
        setSelectedConversationId(conversationIdParam);
        return;
      }
      if (toUserId) {
        const existingConversation = conversations.find(
          conv => (conv.clientId === cognitoUserId && conv.freelancerId === toUserId) ||
                 (conv.freelancerId === cognitoUserId && conv.clientId === toUserId)
        );
        if (existingConversation) {
          router.replace(`/client/messages?conversationId=${existingConversation.id}`);
          setSelectedConversationId(existingConversation.id);
          return;
        }
      }
      return;
    }
    
    if (!toUserId || !jobId) return;
    
    try {
      setIsLoading(true);
      
      let existingConversation = conversations.find(
        conv => (conv.clientId === cognitoUserId && conv.freelancerId === toUserId) ||
               (conv.freelancerId === cognitoUserId && conv.clientId === toUserId)
      );
      
      if (!existingConversation) {
        const serverConversations = await messageService.listMyConversations(cognitoUserId);
        existingConversation = serverConversations.find(
          conv => (conv.clientId === cognitoUserId && conv.freelancerId === toUserId) ||
                 (conv.freelancerId === cognitoUserId && conv.clientId === toUserId)
        );
        
        if (serverConversations.length > 0) {
          setConversations(prev => {
            const existingIds = new Set(prev.map(c => c.id));
            const newConversations = serverConversations.filter(c => !existingIds.has(c.id));
            return [...prev, ...newConversations];
          });
        }
      }
      
      if (existingConversation) {
        router.replace(`/client/messages?conversationId=${existingConversation.id}`);
        setSelectedConversationId(existingConversation.id);
        return;
      }
      
      const conversationId = await messageService.createConversation(
        jobId,
        cognitoUserId,
        toUserId
      );
      
      if (!conversationId) {
        throw new Error('Failed to create conversation');
      }
      
      const updatedConversations = await messageService.listMyConversations(cognitoUserId);
      setConversations(updatedConversations);
      
      const newConversation = updatedConversations.find(conv => conv.id === conversationId);
      if (newConversation) {
        setSelectedConversationId(conversationId);
        router.replace(`/client/messages?conversationId=${conversationId}`);
      }
      
    } catch (error) {
      console.error('Error initializing conversation:', error);
      toast.error('Failed to start conversation');
    } finally {
      setIsLoading(false);
    }
  }, [cognitoUserId, searchParams, conversations, router]);

  const handleSendMessage = useCallback(async (conversationId: string, content: string) => {
    if (!cognitoUserId || !selectedConversation) return;
    
    try {
      setIsSending(true);
      const otherParticipant = selectedConversation.participants.find(
        p => p.id !== cognitoUserId
      );
      
      if (!otherParticipant) {
        throw new Error('Could not find the other participant in the conversation');
      }
      
      await messageService.sendMessage(
        conversationId,
        cognitoUserId,
        otherParticipant.id,
        content
      );
      
      await fetchConversations();
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
      throw error;
    } finally {
      setIsSending(false);
    }
  }, [cognitoUserId, selectedConversation, fetchConversations]);

  const handleLoadMoreMessages = useCallback(async (conversationId: string, before: Date) => {
    if (!cognitoUserId) return [];
    
    try {
      const conversations = await messageService.listMyConversations(cognitoUserId);
      const conversation = conversations.find(conv => conv.id === conversationId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const filteredMessages = (conversation.messages || []).filter(msg => 
        new Date(msg.createdAt) < before
      );

      return filteredMessages.map(msg => ({
        id: msg.id,
        content: msg.messageText || '',
        senderId: msg.senderId,
        receiverId: msg.receiverId || '',
        conversationId: msg.conversationId || conversationId,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt || msg.createdAt,
        status: 'delivered' as const,
        type: (msg.type === 'image' ? 'image' : msg.type === 'file' ? 'file' : 'text') as 'text' | 'file' | 'image',
        sender: {
          id: msg.senderId,
          name: msg.sender?.name || 'Unknown',
          email: msg.sender?.email || '',
          role: (msg.sender?.role as 'CLIENT' | 'FREELANCER') || 'CLIENT',
          avatar: msg.sender?.avatar,
          isOnline: msg.sender?.isOnline || false
        },
        receiver: {
          id: msg.receiverId || '',
          name: msg.receiver?.name || 'Unknown',
          email: msg.receiver?.email || '',
          role: (msg.receiver?.role as 'CLIENT' | 'FREELANCER') || 'CLIENT',
          avatar: msg.receiver?.avatar,
          isOnline: msg.receiver?.isOnline || false
        },
        fileInfo: msg.fileInfo ? {
          name: msg.fileInfo.name || 'file',
          type: msg.fileInfo.type || 'application/octet-stream',
          size: msg.fileInfo.size || 0,
          url: msg.fileInfo.url || ''
        } : undefined
      }));
    } catch (error) {
      console.error('Error loading more messages:', error);
      toast.error('Failed to load messages');
      return [];
    }
  }, [cognitoUserId]);

  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);
  
  useEffect(() => {
    if (conversations.length > 0) {
      initializeConversation();
    }
  }, [conversations, initializeConversation]);

  if (isLoading || !user) {
    return (
      <div className="h-full flex items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-8rem)]">
      {selectedConversation && (
        <div 
          className="hidden" 
          data-testid="selected-conversation" 
          data-conversation-id={selectedConversation.id} 
        />
      )}
      
      <MessagingPage
        currentUser={currentUser}
        initialConversations={messagingConversations}
        onSendMessage={handleSendMessage}
        onLoadMoreMessages={handleLoadMoreMessages}
        isSending={isSending}
        className="h-full"
      />
    </div>
  );
}
