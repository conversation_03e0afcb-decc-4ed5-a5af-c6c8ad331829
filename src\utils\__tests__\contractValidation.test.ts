import {
  validateStatusTransition,
  validateContractAction,
  validateWorkSubmission,
  validateContractData,
  isContractFinal,
  getNextExpectedAction
} from '../contractValidation';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { UserRole } from '@/types/features/auth/auth.types';

describe('contractValidation', () => {
  describe('validateStatusTransition', () => {
    it('should allow valid transitions for freelancers', () => {
      const result = validateStatusTransition(
        ContractStatus.PENDING_FREELANCER_ACCEPTANCE,
        ContractStatus.ACTIVE,
        UserRole.FREELANCER
      );
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid transitions', () => {
      const result = validateStatusTransition(
        ContractStatus.DRAFT,
        ContractStatus.COMPLETED,
        UserRole.CLIENT
      );
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_STATUS_TRANSITION');
    });

    it('should reject transitions by wrong user role', () => {
      const result = validateStatusTransition(
        ContractStatus.PENDING_FREELANCER_ACCEPTANCE,
        ContractStatus.ACTIVE,
        UserRole.CLIENT
      );
      expect(result.isValid).toBe(false);
    });
  });

  describe('validateContractAction', () => {
    const mockContract = {
      id: 'contract-1',
      clientId: 'client-1',
      freelancerId: 'freelancer-1',
      status: ContractStatus.PENDING_FREELANCER_ACCEPTANCE,
      title: 'Test Contract',
      description: 'Test Description',
      budget: 1000,
      type: 'FIXED_PRICE' as any,
      terms: 'Test terms',
      startDate: '2024-01-01',
      jobId: 'job-1',
      proposalId: 'proposal-1',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    };

    it('should allow freelancer to accept pending contract', () => {
      const result = validateContractAction('accept', {
        contract: mockContract,
        userRole: UserRole.FREELANCER,
        userId: 'freelancer-1'
      });
      expect(result.isValid).toBe(true);
    });

    it('should reject unauthorized user actions', () => {
      const result = validateContractAction('accept', {
        contract: mockContract,
        userRole: UserRole.FREELANCER,
        userId: 'other-user'
      });
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('UNAUTHORIZED');
    });

    it('should reject invalid action for contract status', () => {
      const result = validateContractAction('submitWork', {
        contract: mockContract,
        userRole: UserRole.FREELANCER,
        userId: 'freelancer-1'
      });
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_ACTION');
    });
  });

  describe('validateWorkSubmission', () => {
    it('should validate correct work submission', () => {
      const submission = {
        contractId: 'contract-1',
        description: 'This is a detailed description of the completed work',
        attachments: ['file1.pdf'],
        links: ['https://example.com']
      };
      const result = validateWorkSubmission(submission);
      expect(result.isValid).toBe(true);
    });

    it('should reject submission with short description', () => {
      const submission = {
        contractId: 'contract-1',
        description: 'Short',
        attachments: ['file1.pdf']
      };
      const result = validateWorkSubmission(submission);
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_DESCRIPTION');
    });

    it('should reject submission without deliverables', () => {
      const submission = {
        contractId: 'contract-1',
        description: 'This is a detailed description of the completed work'
      };
      const result = validateWorkSubmission(submission);
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('NO_DELIVERABLES');
    });

    it('should reject submission with invalid links', () => {
      const submission = {
        contractId: 'contract-1',
        description: 'This is a detailed description of the completed work',
        links: ['invalid-url', 'https://valid.com']
      };
      const result = validateWorkSubmission(submission);
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_LINKS');
    });
  });

  describe('validateContractData', () => {
    it('should validate correct contract data', () => {
      const contract = {
        title: 'Test Contract',
        description: 'This is a detailed contract description',
        budget: 1000,
        clientId: 'client-1',
        freelancerId: 'freelancer-1'
      };
      const result = validateContractData(contract);
      expect(result.isValid).toBe(true);
    });

    it('should reject contract with short title', () => {
      const contract = {
        title: 'AB',
        description: 'This is a detailed contract description',
        budget: 1000,
        clientId: 'client-1',
        freelancerId: 'freelancer-1'
      };
      const result = validateContractData(contract);
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_TITLE');
    });

    it('should reject contract with zero budget', () => {
      const contract = {
        title: 'Test Contract',
        description: 'This is a detailed contract description',
        budget: 0,
        clientId: 'client-1',
        freelancerId: 'freelancer-1'
      };
      const result = validateContractData(contract);
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_BUDGET');
    });

    it('should reject contract with same client and freelancer', () => {
      const contract = {
        title: 'Test Contract',
        description: 'This is a detailed contract description',
        budget: 1000,
        clientId: 'user-1',
        freelancerId: 'user-1'
      };
      const result = validateContractData(contract);
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('SAME_PARTIES');
    });
  });

  describe('isContractFinal', () => {
    it('should identify final contract statuses', () => {
      expect(isContractFinal(ContractStatus.PAID)).toBe(true);
      expect(isContractFinal(ContractStatus.CANCELLED)).toBe(true);
      expect(isContractFinal(ContractStatus.ACTIVE)).toBe(false);
      expect(isContractFinal(ContractStatus.COMPLETED)).toBe(false);
    });
  });

  describe('getNextExpectedAction', () => {
    const mockContract = {
      id: 'contract-1',
      clientId: 'client-1',
      freelancerId: 'freelancer-1',
      status: ContractStatus.PENDING_FREELANCER_ACCEPTANCE,
      title: 'Test Contract',
      description: 'Test Description',
      budget: 1000,
      type: 'FIXED_PRICE' as any,
      terms: 'Test terms',
      startDate: '2024-01-01',
      jobId: 'job-1',
      proposalId: 'proposal-1',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    };

    it('should return correct action for freelancer with pending contract', () => {
      const action = getNextExpectedAction(
        mockContract,
        UserRole.FREELANCER,
        'freelancer-1'
      );
      expect(action).toBe('Accept or decline the contract');
    });

    it('should return correct action for client with pending contract', () => {
      const action = getNextExpectedAction(
        mockContract,
        UserRole.CLIENT,
        'client-1'
      );
      expect(action).toBe('Waiting for freelancer response');
    });

    it('should return correct action for active contract', () => {
      const activeContract = { ...mockContract, status: ContractStatus.ACTIVE };
      const action = getNextExpectedAction(
        activeContract,
        UserRole.FREELANCER,
        'freelancer-1'
      );
      expect(action).toBe('Submit your completed work');
    });
  });
});
