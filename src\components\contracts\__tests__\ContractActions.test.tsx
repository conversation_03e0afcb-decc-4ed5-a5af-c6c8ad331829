import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ContractActions } from '../ContractActions';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { UserRole } from '@/types/features/auth/auth.types';
import contractService from '@/api/contracts/contract.service';

// Mock the contract service
jest.mock('@/api/contracts/contract.service', () => ({
  updateContractStatus: jest.fn(),
  updateJobStatus: jest.fn(),
  createPayment: jest.fn(),
}));

// Mock the toast hook
jest.mock('@/components/ui/toast', () => ({
  useToast: () => ({
    showToast: jest.fn(),
  }),
}));

const mockContract = {
  id: 'contract-1',
  clientId: 'client-1',
  freelancerId: 'freelancer-1',
  jobId: 'job-1',
  status: ContractStatus.PENDING_FREELANCER_ACCEPTANCE,
  title: 'Test Contract',
  description: 'Test Description',
  budget: 1000,
  type: 'FIXED_PRICE' as any,
  terms: 'Test terms',
  startDate: '2024-01-01',
  proposalId: 'proposal-1',
  createdAt: '2024-01-01',
  updatedAt: '2024-01-01'
};

describe('ContractActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Freelancer Actions', () => {
    it('should show accept and decline buttons for pending contract', () => {
      render(
        <ContractActions
          contract={mockContract}
          userRole={UserRole.FREELANCER}
          userId="freelancer-1"
        />
      );

      expect(screen.getByText('Accept Contract')).toBeInTheDocument();
      expect(screen.getByText('Decline Contract')).toBeInTheDocument();
    });

    it('should not show actions for unauthorized freelancer', () => {
      render(
        <ContractActions
          contract={mockContract}
          userRole={UserRole.FREELANCER}
          userId="other-freelancer"
        />
      );

      expect(screen.queryByText('Accept Contract')).not.toBeInTheDocument();
      expect(screen.queryByText('Decline Contract')).not.toBeInTheDocument();
    });

    it('should handle contract acceptance', async () => {
      const mockUpdateContractStatus = contractService.updateContractStatus as jest.Mock;
      const mockUpdateJobStatus = contractService.updateJobStatus as jest.Mock;
      const onStatusUpdate = jest.fn();
      const onJobStatusUpdate = jest.fn();

      mockUpdateContractStatus.mockResolvedValue({ id: 'contract-1', status: ContractStatus.ACTIVE });
      mockUpdateJobStatus.mockResolvedValue({ id: 'job-1', status: 'IN_PROGRESS' });

      render(
        <ContractActions
          contract={mockContract}
          userRole={UserRole.FREELANCER}
          userId="freelancer-1"
          onStatusUpdate={onStatusUpdate}
          onJobStatusUpdate={onJobStatusUpdate}
        />
      );

      const acceptButton = screen.getByText('Accept Contract');
      fireEvent.click(acceptButton);

      await waitFor(() => {
        expect(mockUpdateContractStatus).toHaveBeenCalledWith('contract-1', ContractStatus.ACTIVE);
        expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-1', 'IN_PROGRESS');
        expect(onStatusUpdate).toHaveBeenCalledWith(ContractStatus.ACTIVE);
        expect(onJobStatusUpdate).toHaveBeenCalledWith('IN_PROGRESS');
      });
    });

    it('should show submit work button for active contract', () => {
      const activeContract = { ...mockContract, status: ContractStatus.ACTIVE };
      
      render(
        <ContractActions
          contract={activeContract}
          userRole={UserRole.FREELANCER}
          userId="freelancer-1"
        />
      );

      expect(screen.getByText('Submit Work')).toBeInTheDocument();
    });
  });

  describe('Client Actions', () => {
    it('should show approve and request changes buttons for submitted work', () => {
      const submittedContract = { ...mockContract, status: ContractStatus.WORK_SUBMITTED };
      
      render(
        <ContractActions
          contract={submittedContract}
          userRole={UserRole.CLIENT}
          userId="client-1"
        />
      );

      expect(screen.getByText('Approve Work')).toBeInTheDocument();
      expect(screen.getByText('Request Changes')).toBeInTheDocument();
    });

    it('should handle work approval', async () => {
      const mockUpdateContractStatus = contractService.updateContractStatus as jest.Mock;
      const mockUpdateJobStatus = contractService.updateJobStatus as jest.Mock;
      const onStatusUpdate = jest.fn();
      const onJobStatusUpdate = jest.fn();

      mockUpdateContractStatus.mockResolvedValue({ id: 'contract-1', status: ContractStatus.COMPLETED });
      mockUpdateJobStatus.mockResolvedValue({ id: 'job-1', status: 'COMPLETED' });

      const submittedContract = { ...mockContract, status: ContractStatus.WORK_SUBMITTED };
      
      render(
        <ContractActions
          contract={submittedContract}
          userRole={UserRole.CLIENT}
          userId="client-1"
          onStatusUpdate={onStatusUpdate}
          onJobStatusUpdate={onJobStatusUpdate}
        />
      );

      const approveButton = screen.getByText('Approve Work');
      fireEvent.click(approveButton);

      await waitFor(() => {
        expect(mockUpdateContractStatus).toHaveBeenCalledWith('contract-1', ContractStatus.COMPLETED);
        expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-1', 'COMPLETED');
        expect(onStatusUpdate).toHaveBeenCalledWith(ContractStatus.COMPLETED);
        expect(onJobStatusUpdate).toHaveBeenCalledWith('COMPLETED');
      });
    });

    it('should show mark as paid button for completed contract', () => {
      const completedContract = { ...mockContract, status: ContractStatus.COMPLETED };
      
      render(
        <ContractActions
          contract={completedContract}
          userRole={UserRole.CLIENT}
          userId="client-1"
        />
      );

      expect(screen.getByText('Mark as Paid')).toBeInTheDocument();
    });

    it('should handle payment processing', async () => {
      const mockCreatePayment = contractService.createPayment as jest.Mock;
      const mockUpdateContractStatus = contractService.updateContractStatus as jest.Mock;
      const onStatusUpdate = jest.fn();

      mockCreatePayment.mockResolvedValue({ id: 'payment-1' });
      mockUpdateContractStatus.mockResolvedValue({ id: 'contract-1', status: ContractStatus.PAID });

      const completedContract = { ...mockContract, status: ContractStatus.COMPLETED };
      
      render(
        <ContractActions
          contract={completedContract}
          userRole={UserRole.CLIENT}
          userId="client-1"
          onStatusUpdate={onStatusUpdate}
        />
      );

      const payButton = screen.getByText('Mark as Paid');
      fireEvent.click(payButton);

      await waitFor(() => {
        expect(mockCreatePayment).toHaveBeenCalledWith('contract-1', 1000);
        expect(mockUpdateContractStatus).toHaveBeenCalledWith('contract-1', ContractStatus.PAID);
        expect(onStatusUpdate).toHaveBeenCalledWith(ContractStatus.PAID);
      });
    });
  });

  describe('Cancel Actions', () => {
    it('should show cancel button for active contract', () => {
      const activeContract = { ...mockContract, status: ContractStatus.ACTIVE };
      
      render(
        <ContractActions
          contract={activeContract}
          userRole={UserRole.CLIENT}
          userId="client-1"
        />
      );

      expect(screen.getByText('Cancel Contract')).toBeInTheDocument();
    });

    it('should handle contract cancellation', async () => {
      const mockUpdateContractStatus = contractService.updateContractStatus as jest.Mock;
      const mockUpdateJobStatus = contractService.updateJobStatus as jest.Mock;
      const onStatusUpdate = jest.fn();
      const onJobStatusUpdate = jest.fn();

      mockUpdateContractStatus.mockResolvedValue({ id: 'contract-1', status: ContractStatus.CANCELLED });
      mockUpdateJobStatus.mockResolvedValue({ id: 'job-1', status: 'CANCELLED' });

      const activeContract = { ...mockContract, status: ContractStatus.ACTIVE };
      
      render(
        <ContractActions
          contract={activeContract}
          userRole={UserRole.CLIENT}
          userId="client-1"
          onStatusUpdate={onStatusUpdate}
          onJobStatusUpdate={onJobStatusUpdate}
        />
      );

      const cancelButton = screen.getByText('Cancel Contract');
      fireEvent.click(cancelButton);

      await waitFor(() => {
        expect(mockUpdateContractStatus).toHaveBeenCalledWith('contract-1', ContractStatus.CANCELLED);
        expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-1', 'CANCELLED');
        expect(onStatusUpdate).toHaveBeenCalledWith(ContractStatus.CANCELLED);
        expect(onJobStatusUpdate).toHaveBeenCalledWith('CANCELLED');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const mockUpdateContractStatus = contractService.updateContractStatus as jest.Mock;
      mockUpdateContractStatus.mockRejectedValue(new Error('API Error'));

      render(
        <ContractActions
          contract={mockContract}
          userRole={UserRole.FREELANCER}
          userId="freelancer-1"
        />
      );

      const acceptButton = screen.getByText('Accept Contract');
      fireEvent.click(acceptButton);

      await waitFor(() => {
        expect(mockUpdateContractStatus).toHaveBeenCalled();
        // Toast error should be shown, but we can't easily test that without more setup
      });
    });
  });
});
