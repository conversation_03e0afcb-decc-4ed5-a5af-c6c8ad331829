import { useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Icon } from './Icon';
import { Pagination } from './Pagination';
import type { IconName } from '@/types/ui';
import type { 
  Column as ColumnType, 
  TableProps as TablePropsType,
  TableData,
  AccessorFn,
  CellRenderer
} from '@/types/components/Table';

export type { AccessorFn, CellRenderer, TableData };

/**
 * Column configuration for the Table component
 * @template T The type of data in the table rows
 * @deprecated Use ColumnType from '@/types/components/Table' instead
 */
export type Column<T> = ColumnType<T>;

/**
 * Table component props
 * @template T The type of data in the table rows
 * @deprecated Use TableProps from '@/types/components/Table' instead
 */
export type TableProps<T> = Omit<TablePropsType<T>, 'keyField' | 'rowKey'> & {
  /** @deprecated Use rowKey instead */
  keyField?: keyof T;
};

/**
 * A flexible table component that can display any type of data
 * @template T The type of data in the table rows
 */
export function Table<T extends TableData = TableData>({
  columns,
  data,
  onRowClick,
  emptyState,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  rowClassName = '',
  isLoading = false,
  loadingRows = 5,
  keyField = 'id' as keyof T,
  sortConfig,
  pagination,
}: TableProps<T>) {
  const handleRowClick = (row: T, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    onRowClick?.(row, event);
  };


  const renderCell = (row: T, column: Column<T>) => {
    let value: unknown;
    
    if (typeof column.accessor === 'function') {
      value = column.accessor(row);
    } else if (column.accessor in row) {
      value = row[column.accessor as keyof T];
    } else {
      console.warn(`Column accessor '${String(column.accessor)}' not found in row data`);
      return '-';
    }

    if (column.cell) {
      return column.cell(value, row) ?? '-';
    }

    return typeof value === 'string' || typeof value === 'number' ? value : '-';
  };

  const loadingData = useMemo(
    () => Array(loadingRows).fill({} as T),
    [loadingRows]
  );

  const paginationEnabled = pagination?.enabled === true;
  const pageSize = pagination?.pageSize || 5;
  const currentPage = pagination?.currentPage || 1;
  const calculatedTotalPages = pagination?.totalPages || Math.ceil((pagination?.totalItems || data.length) / pageSize);

  const displayData = useMemo(() => {
    if (isLoading) return loadingData;
    return data;
  }, [isLoading, loadingData, data]);
  
  const handlePageChange = (page: number) => {
    if (pagination?.onPageChange) {
      pagination.onPageChange(page);
    }
  };
  const isEmpty = !isLoading && data.length === 0;

  if (isEmpty) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
        {emptyState?.icon && (
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted mb-4">
            <Icon 
              name={emptyState.icon as IconName}
              className="h-6 w-6 text-muted-foreground" 
            />
          </div>
        )}
        <h3 className="mt-2 text-sm font-medium text-foreground">
          {emptyState?.title || 'No data available'}
        </h3>
        {emptyState?.description && (
          <p className="mt-1 text-sm text-muted-foreground">
            {emptyState.description}
          </p>
        )}
        {emptyState?.action && (
          <div className="mt-6">
            {emptyState.action}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col w-full', className)}>
      <div className="overflow-x-auto">
        <table className={cn('w-full border-collapse', className)}>
          <thead>
            <tr className={cn('bg-muted/50', headerClassName)}>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={cn(
                    'px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider',
                    column.headerClassName,
                    column.sortable ? 'cursor-pointer hover:text-foreground' : '',
                    column.className
                  )}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && sortConfig?.onSort(column.accessor as keyof T)}
                >
                  <div className="flex items-center">
                    {column.header}
                    {column.sortable && sortConfig && (
                      <span className="ml-2">
                        {sortConfig?.key === column.accessor && (
                          <Icon
                            name={
                              sortConfig.direction === 'asc' ? 'ChevronUp' as const : 'ChevronDown' as const
                            }
                            className="ml-1 h-3 w-3"
                          />
                        )}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className={cn('divide-y divide-border', bodyClassName)}>
            {displayData.map((row, rowIndex) => (
              <tr
                key={isLoading ? `loading-${rowIndex}` : String(row[keyField] || rowIndex)}
                className={cn(
                  'transition-colors',
                  onRowClick && 'cursor-pointer hover:bg-muted/50',
                  typeof rowClassName === 'function' ? rowClassName(row, rowIndex) : rowClassName,
                  isLoading && 'opacity-50 animate-pulse'
                )}
                onClick={(e) => !isLoading && handleRowClick(row, e)}
              >
                {columns.map((column, colIndex) => (
                  <td
                    key={colIndex}
                    className={cn(
                      'px-4 py-3 text-sm text-foreground',
                      column.cellClassName
                    )}
                  >
                    {isLoading ? (
                      <div className="h-4 bg-muted rounded w-3/4" />
                    ) : (
                      renderCell(row, column)
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {paginationEnabled && (
        <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 px-4 py-3 border-t border-border">
          <div className="text-sm text-muted-foreground">
            {pagination?.totalItems !== undefined ? (
              <>
                Showing <span className="font-medium">
                  {((currentPage - 1) * pageSize) + 1}
                </span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, pagination.totalItems)}
                </span>{' '}
                of <span className="font-medium">{pagination.totalItems}</span> entries
              </>
            ) : (
              <span>{data.length} entries</span>
            )}
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={calculatedTotalPages}
            onPageChange={handlePageChange}
            showFirstLast={pagination.showFirstLast ?? true}
            showPrevNext={pagination.showPrevNext ?? true}
            maxVisiblePages={pagination.maxVisiblePages ?? 5}
          />
        </div>
      )}
    </div>
  );
}

/**
 * Compound component type for the Table component
 */
type TableComponent = typeof Table & {
  // Add any compound components here if needed
};

export default Table as TableComponent;
