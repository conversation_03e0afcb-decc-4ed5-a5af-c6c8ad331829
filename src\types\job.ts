export type JobStatus =
  | 'OPEN'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';

export type JobCategory =
  | 'WEB_DEVELOPMENT'
  | 'MOBILE_DEVELOPMENT'
  | 'DESIGN'
  | 'WRITING'
  | 'MARKETING'
  | 'BUSINESS'
  | 'ACCOUNTING'
  | 'LEGAL'
  | 'OTHER';

import { JobProposal, JobProposalConnection } from './proposal.types';

export interface BaseJob {
  id: string;
  title: string;
  description: string;
  category: JobCategory;
  budget: number;
  deadline: string;
  clientId: string;
  status: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
  createdAt: string;
  updatedAt?: string;
  client?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface Job extends BaseJob {
  proposals?: JobProposalConnection;
  proposalCount?: number;
}

export interface JobWithProposalList extends BaseJob {
  proposals: JobProposal[];
  proposalCount: number;
}

export interface JobWithProposals extends Job {
  proposals: JobProposalConnection;
}

export interface CreateJobInput {
  title: string;
  description: string;
  category: JobCategory;
  budget: number;
  deadline: string;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
  clientId: string;
}

export interface UpdateJobInput {
  id: string;
  title?: string;
  description?: string;
  category?: JobCategory;
  budget?: number;
  deadline?: string;
  status?: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
}

export interface JobFilter {
  clientId?: string;
  category?: JobCategory;
  minBudget?: number;
  maxBudget?: number;
  isRemote?: boolean;
  status?: JobStatus;
  searchTerm?: string;
  includeProposals?: boolean;
  nextToken?: string;
  limit?: number;
}
