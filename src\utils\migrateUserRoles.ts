/**
 * Utility to migrate user roles from database to Cognito custom:role attributes
 * This is a development/admin utility to fix existing users missing the custom:role attribute
 */

import { generateClient } from "aws-amplify/api";
import * as queries from "@/lib/graphql/queries";
import { UserRole } from "@/types/enums";

const client = generateClient();

interface DatabaseUser {
  id: string;
  email: string;
  name: string;
  role: string;
}

interface MigrationResult {
  success: boolean;
  email: string;
  role?: string;
  error?: string;
}

/**
 * Get all users from the database
 */
async function getAllUsersFromDatabase(): Promise<DatabaseUser[]> {
  try {
    const result = await client.graphql({
      query: queries.listUsers,
      authMode: 'userPool'
    }) as { data: { listUsers?: { items: DatabaseUser[] } } };
    
    return result.data?.listUsers?.items || [];
  } catch (error) {
    console.error('Error fetching users from database:', error);
    throw error;
  }
}

/**
 * Update a single user's Cognito custom:role attribute
 */
async function updateCognitoUserRole(email: string, role: UserRole): Promise<void> {
  console.log(`Would update Cognito user ${email} with role ${role}`);
  
  // In a real implementation, you would use AWS SDK:
  // const { CognitoIdentityProviderClient, AdminUpdateUserAttributesCommand } = require('@aws-sdk/client-cognito-identity-provider');
  // const client = new CognitoIdentityProviderClient({ region: 'your-region' });
  // await client.send(new AdminUpdateUserAttributesCommand({
  //   UserPoolId: 'your-user-pool-id',
  //   Username: email,
  //   UserAttributes: [
  //     {
  //       Name: 'custom:role',
  //       Value: role
  //     }
  //   ]
  // }));
}

/**
 * Migrate roles for all users from database to Cognito
 */
export async function migrateAllUserRoles(): Promise<MigrationResult[]> {
  const results: MigrationResult[] = [];
  
  try {
    const dbUsers = await getAllUsersFromDatabase();
    console.log(`Found ${dbUsers.length} users in database`);
    
    for (const user of dbUsers) {
      try {
        // Validate role
        if (!user.role || !Object.values(UserRole).includes(user.role as UserRole)) {
          results.push({
            success: false,
            email: user.email,
            error: `Invalid role in database: ${user.role}`
          });
          continue;
        }
        
        await updateCognitoUserRole(user.email, user.role as UserRole);
        
        results.push({
          success: true,
          email: user.email,
          role: user.role
        });
        
        console.log(`✓ Migrated ${user.email} with role ${user.role}`);
        
      } catch (error) {
        results.push({
          success: false,
          email: user.email,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.error(`✗ Failed to migrate ${user.email}:`, error);
      }
    }
    
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  }
  
  return results;
}

/**
 * Migrate role for a specific user
 */
export async function migrateUserRole(email: string, role: UserRole): Promise<MigrationResult> {
  try {
    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      return {
        success: false,
        email,
        error: `Invalid role: ${role}`
      };
    }
    
    await updateCognitoUserRole(email, role);
    
    console.log(`✓ Migrated ${email} with role ${role}`);
    
    return {
      success: true,
      email,
      role
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`✗ Failed to migrate ${email}:`, error);
    
    return {
      success: false,
      email,
      error: errorMessage
    };
  }
}

/**
 * Development utility to manually set <NAME_EMAIL>
 */
export async function fixHagridRole(): Promise<MigrationResult> {
  return migrateUserRole('<EMAIL>', UserRole.FREELANCER);
}

// Export for console usage
if (typeof window !== 'undefined') {
  (window as any).migrateUserRoles = {
    migrateAllUserRoles,
    migrateUserRole,
    fixHagridRole,
    UserRole
  };
}
