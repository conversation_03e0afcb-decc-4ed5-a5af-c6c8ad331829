"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { generateClient } from "aws-amplify/api";
import * as queries from "@/lib/graphql/queries";
import * as mutations from "@/lib/graphql/mutations";
import type { User } from "@/types/user";
import { UserRole } from "@/types/enums";

interface GetUserResponse {
  data?: {
    getUser?: User | null;
  };
}

const client = generateClient();

export default function OnboardRoleGate({ 
  desiredRole, 
  name 
}: { 
  desiredRole: UserRole; 
  name: string 
}) {
  const { user, updateProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    async function run() {
      const userId = user?.username;
      if (!userId) return;
      
      if (user.attributes?.['custom:role']) return;
      
      try {
        await updateProfile({
          'custom:role': desiredRole
        });
        
        const res = await client.graphql({ 
          query: queries.getUser, 
          variables: { id: userId } 
        });
        
        const existing = (res as GetUserResponse).data?.getUser;
        if (!existing) {
          await client.graphql({
            query: mutations.createUser,
            variables: { 
              input: { 
                id: userId, 
                name, 
                email: user.attributes?.email || "", 
                role: desiredRole 
              } 
            },
            authMode: "userPool",
          });
        }
        
        router.refresh();
      } catch (e) {
        console.error('Error in OnboardRoleGate:', e);
      }
    }
    
    run();
  }, [user, desiredRole, name, router, updateProfile]);

  return null;
}

