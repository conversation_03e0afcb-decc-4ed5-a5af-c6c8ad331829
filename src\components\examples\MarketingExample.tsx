import React from "react";
import { Icon } from "@/components/ui/Icon";
import {
  MarketingLayout,
  HeroSection,
  FeatureSection,
} from "@/components/layouts";
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui";
import { Container } from "@/components/layout";

const features = [
  {
    title: "Find Quality Freelancers",
    description:
      "Connect with skilled professionals from around the world who can help bring your projects to life.",
    icon: <Icon name="Briefcase" size="lg" />,
  },
  {
    title: "Secure Payments",
    description:
      "Our escrow system ensures safe transactions for both clients and freelancers.",
    icon: <Icon name="Shield" size="lg" />,
  },
  {
    title: "24/7 Support",
    description:
      "Get help whenever you need it with our dedicated customer support team.",
    icon: <Icon name="LifeBuoy" size="lg" />,
  },
];

const MarketingExample: React.FC = () => {
  return (
    <MarketingLayout
      heroSection={
        <HeroSection
          subtitle="Welcome to MyVillage"
          title="Connect with talented freelancers worldwide"
          description="Find the perfect freelancer for your project or showcase your skills to potential clients. Join our growing community of professionals."
          actions={
            <>
              <Button size="lg" className="text-lg px-8 py-3">
                Get Started
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8 py-3">
                Learn More
              </Button>
            </>
          }
        />
      }
    >
      {/* Features Section */}
      <FeatureSection
        title="Why Choose MyVillage?"
        description="We provide everything you need to succeed in the freelance marketplace"
        features={features}
      />

      {/* Stats Section */}
      <section className="py-16 bg-muted/50">
        <Container>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">
                10,000+
              </div>
              <p className="text-muted-foreground">Active Freelancers</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">5,000+</div>
              <p className="text-muted-foreground">Completed Projects</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">98%</div>
              <p className="text-muted-foreground">Client Satisfaction</p>
            </div>
          </div>
        </Container>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              What Our Users Say
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Don&apos;t just take our word for it - hear from our satisfied
              clients and freelancers
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                name: "Sarah Johnson",
                role: "Startup Founder",
                content:
                  "MyVillage helped me find the perfect developer for my project. The quality of work exceeded my expectations.",
                rating: 5,
              },
              {
                name: "Mike Chen",
                role: "Freelance Designer",
                content:
                  "As a freelancer, I love how easy it is to find quality projects and get paid securely through MyVillage.",
                rating: 5,
              },
              {
                name: "Emily Davis",
                role: "Marketing Manager",
                content:
                  "The platform is intuitive and the support team is incredibly helpful. Highly recommended!",
                rating: 5,
              },
            ].map((testimonial, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Icon key={i} name="Star" size="sm" className="text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <CardTitle className="text-lg">{testimonial.name}</CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    &ldquo;{testimonial.content}&rdquo;
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-foreground">
        <Container>
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Get Started?
            </h2>
            <p className="mt-4 text-lg opacity-90">
              Join thousands of freelancers and clients who trust MyVillage
            </p>
            <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button
                size="lg"
                variant="secondary"
                className="text-lg px-8 py-3"
              >
                Sign Up as Freelancer
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-8 py-3 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
              >
                Post a Project
              </Button>
            </div>
          </div>
        </Container>
      </section>
    </MarketingLayout>
  );
};

export default MarketingExample;
