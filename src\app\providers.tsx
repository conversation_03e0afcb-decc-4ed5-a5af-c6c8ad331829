"use client";
import React from "react";
import { AuthProvider as NewAuthProvider } from "../lib/auth/AuthContext";
import { ensureAmplifyConfigured } from "../lib/amplifyClient";
import { Toaster } from "@/components/ui/toast";

ensureAmplifyConfigured();

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NewAuthProvider>
      <Toaster>
        {children}
      </Toaster>
    </NewAuthProvider>
  );
}

