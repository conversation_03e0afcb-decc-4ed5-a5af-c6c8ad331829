import React from 'react';
import { Navbar, NavItem } from '@/components/layout/Navbar';
import { Footer } from '@/components/layout/Footer';
import { Container } from '@/components/layout/Container';
import { cn } from '@/lib/utils';

export interface MarketingLayoutProps {
  children: React.ReactNode;
  navigation?: NavItem[];
  className?: string;
  showFooter?: boolean;
  heroSection?: React.ReactNode;
}

const defaultNavigation: NavItem[] = [
  { name: 'Find Jobs', href: '/jobs' },
  { name: 'Find Freelancers', href: '/freelancers' },
  { name: 'How it Works', href: '/how-it-works' },
  { name: 'Pricing', href: '/pricing' },
];

const MarketingLayout: React.FC<MarketingLayoutProps> = ({
  children,
  navigation = defaultNavigation,
  className,
  showFooter = true,
  heroSection,
}) => {
  return (
    <div className="min-h-screen bg-background">
      <Navbar navigation={navigation} />
      
      {heroSection && (
        <section className="relative">
          {heroSection}
        </section>
      )}
      
      <main className={cn('flex-1', className)}>
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
};

export interface HeroSectionProps {
  title: string;
  subtitle?: string;
  description?: string;
  actions?: React.ReactNode;
  backgroundImage?: string;
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  subtitle,
  description,
  actions,
  backgroundImage,
  className,
}) => {
  return (
    <section
      className={cn(
        'relative bg-gradient-to-br from-timberwolf-50 to-sage-100 py-20 lg:py-32',
        className
      )}
      style={
        backgroundImage
          ? {
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }
          : undefined
      }
    >
      {backgroundImage && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm" />
      )}
      
      <Container className="relative">
        <div className="mx-auto max-w-4xl text-center">
          {subtitle && (
            <p className="mb-4 text-sm font-semibold uppercase tracking-wide text-primary">
              {subtitle}
            </p>
          )}
          
          <h1 className="mb-6 text-4xl font-bold tracking-tight text-foreground sm:text-5xl lg:text-6xl">
            {title}
          </h1>
          
          {description && (
            <p className="mb-8 text-xl text-muted-foreground lg:text-2xl">
              {description}
            </p>
          )}
          
          {actions && (
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              {actions}
            </div>
          )}
        </div>
      </Container>
    </section>
  );
};

export interface FeatureItem {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

export interface FeatureSectionProps {
  title?: string;
  description?: string;
  features: FeatureItem[];
  columns?: 2 | 3 | 4;
  className?: string;
}

const FeatureSection: React.FC<FeatureSectionProps> = ({
  title,
  description,
  features,
  columns = 3,
  className,
}) => {
  const gridClasses = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <section className={cn('py-16 lg:py-24', className)}>
      <Container>
        {(title || description) && (
          <div className="mx-auto max-w-3xl text-center mb-16">
            {title && (
              <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                {title}
              </h2>
            )}
            {description && (
              <p className="mt-4 text-lg text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        )}
        
        <div className={cn('grid gap-8', gridClasses[columns])}>
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              {feature.icon && (
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                  {feature.icon}
                </div>
              )}
              <h3 className="mb-2 text-xl font-semibold text-foreground">
                {feature.title}
              </h3>
              <p className="text-muted-foreground">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
};

export { MarketingLayout, HeroSection, FeatureSection };
