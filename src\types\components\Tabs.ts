import { ReactNode } from 'react';

export interface TabItem {
  /** Unique identifier for the tab */
  id: string;
  /** Display label for the tab */
  label: string;
  /** Content to display when the tab is active */
  content: ReactNode;
  /** Whether the tab is disabled */
  disabled?: boolean;
  /** Optional badge to display with the tab */
  badge?: string | number;
  /** Additional CSS classes for the tab */
  className?: string;
  /** Icon to display before the label */
  icon?: ReactNode;
}

export interface TabsProps {
  /** Array of tab items */
  items: TabItem[];
  /** ID of the initially active tab */
  defaultTab?: string;
  /** Callback when the active tab changes */
  onChange?: (tabId: string) => void;
  /** Additional CSS classes for the tabs container */
  className?: string;
  /** Additional CSS classes for the tab list */
  tabListClassName?: string;
  /** Additional CSS classes for individual tabs */
  tabClassName?: string;
  /** Additional CSS classes for the active tab */
  activeTabClassName?: string;
  /** Additional CSS classes for the tab content */
  contentClassName?: string;
  /** Whether to use the full width for tabs */
  fullWidth?: boolean;
  /** Position of the tab list */
  position?: 'top' | 'left' | 'right' | 'bottom';
}
