"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import type { JobProposal } from "@/types/proposal";
import { proposalService } from "@/api/proposals/proposal.service";
import { formatDistanceToNow } from "date-fns";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";
import { Table } from "@/components/ui/Table";
import type { Column } from "@/types/components/Table";
import { Badge } from "@/components/ui/Badge";
import { Card, CardContent } from "@/components/ui/Card";
import {
  Search,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { ContentHeader } from "@/components/layout/ContentHeader";

type JobData = {
  id: string;
  title: string;
  budget: number;
  status: string;
  createdAt: string;
  client: {
    id: string;
    name: string;
    email: string;
  } | null;
} & Record<string, unknown>;

interface FlattenedProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  jobTitle: string;
  clientName: string;
  jobBudget: number;
  jobStatus: string;
  jobCreatedAt: string;
  _job: string;
  
  [key: string]: string | number | boolean | Record<string, unknown> | null | undefined;
}

type StatusFilter = "" | "PENDING" | "ACCEPTED" | "REJECTED";

const statusVariantMap = {
  PENDING: "outline",
  ACCEPTED: "success",
  REJECTED: "destructive",
} as const;

const statusIconMap = {
  PENDING: <Clock className="h-4 w-4 mr-1" />,
  ACCEPTED: <CheckCircle className="h-4 w-4 mr-1" />,
  REJECTED: <XCircle className="h-4 w-4 mr-1" />,
};

const MyProposalsPage = () => {
  const auth = useAuth();
  const { isAuthenticated, user, loading: authLoading, cognitoUserId } = auth;

  const [proposals, setProposals] = useState<FlattenedProposal[]>([]);
  const [filteredProposals, setFilteredProposals] = useState<FlattenedProposal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("");
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const fetchProposals = useCallback(async () => {
    try {
      setIsLoading(true);
      if (!cognitoUserId) {
        throw new Error("User not authenticated");
      }
      console.log("Fetching proposals for user:", cognitoUserId);
      const response = await proposalService.listMyProposals(cognitoUserId);
      console.log("Raw proposals data:", JSON.stringify(response, null, 2));

      console.log('Proposal statuses from API:', response.map(p => ({
        id: p.id,
        status: p.status,
        jobTitle: p.job?.title
      })));

      const getJobData = (job: JobProposal["job"] | undefined): JobData => {
        if (!job) {
          return {
            id: 'unknown',
            title: 'Job not found',
            budget: 0,
            status: 'UNKNOWN',
            createdAt: new Date().toISOString(),
            client: null
          } as JobData;
        }
        
        return {
          id: job.id || 'unknown',
          title: job.title || 'Job not found',
          budget: typeof job.budget === 'number' ? job.budget : 0,
          status: job.status || 'UNKNOWN',
          createdAt: job.createdAt || new Date().toISOString(),
          client: job.client || null
        } as JobData;
      };

      const typedData: FlattenedProposal[] = response.map(proposal => {
        const jobData = getJobData(proposal.job);
        const clientName = jobData.client?.name || 'N/A';
        
        const flattened: FlattenedProposal = {
          id: proposal.id || String(Math.random()),
          jobId: proposal.jobId || '',
          freelancerId: proposal.freelancerId || '',
          coverLetter: proposal.coverLetter || '',
          bidAmount: typeof proposal.bidAmount === 'number' ? proposal.bidAmount : 0,
          status: proposal.status || '',
          createdAt: proposal.createdAt || new Date().toISOString(),
          updatedAt: proposal.updatedAt || new Date().toISOString(),
          
          jobTitle: jobData.title,
          clientName,
          jobBudget: jobData.budget,
          jobStatus: jobData.status,
          jobCreatedAt: jobData.createdAt,
          
          _job: JSON.stringify(jobData)
        };
        
        return flattened;
      });

      if (typedData.length > 0) {
        const first = typedData[0];
        console.log("First proposal structure:", {
          id: first.id,
          jobTitle: first.jobTitle,
          clientName: first.clientName,
          jobBudget: first.jobBudget,
          proposedRate: first.proposedRate,
          status: first.status,
        });

        console.log("All proposal statuses:", typedData.map(p => ({
          id: p.id,
          status: p.status,
          rawStatus: response.find(r => r.id === p.id)?.status
        })));
      }

      setProposals(typedData);
      setFilteredProposals(typedData);
    } catch (err) {
      console.error("Error fetching proposals:", err);
      toast.error("Failed to load proposals. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [cognitoUserId]);

  useEffect(() => {
    const filtered = proposals.filter((proposal) => {
      const matchesSearch =
        !searchTerm ||
        proposal.jobTitle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proposal.coverLetter
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase());

      const matchesStatus = !statusFilter || proposal.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    setFilteredProposals(filtered);
    setCurrentPage(1);
  }, [proposals, searchTerm, statusFilter]);

  useEffect(() => {
    console.log('Auth state changed:', { 
      isAuthenticated, 
      userRole: user?.attributes?.['custom:role'],
      cognitoUserId
    });
    
    if (isAuthenticated && user?.attributes?.['custom:role'] === 'FREELANCER') {
      console.log('Fetching proposals...');
      fetchProposals().catch(err => {
        console.error('Error in fetchProposals:', err);
      });
    } else {
      console.log('Not fetching proposals. Conditions not met:', {
        isAuthenticated,
        userRole: user?.attributes?.['custom:role']
      });
    }
  }, [isAuthenticated, user, fetchProposals, cognitoUserId]);

  const totalPages = Math.ceil(filteredProposals.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProposals = filteredProposals.slice(startIndex, endIndex);

  useEffect(() => {
    const params = new URLSearchParams();
    if (searchTerm) params.set("search", searchTerm);
    if (statusFilter) params.set("status", statusFilter);
    if (currentPage > 1) params.set("page", currentPage.toString());

    const queryString = params.toString();
    const newUrl = queryString ? `?${queryString}` : window.location.pathname;

    window.history.replaceState({}, "", newUrl);
  }, [searchTerm, statusFilter, currentPage]);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  console.log("Current proposals:", currentProposals);
  const columns: Column<FlattenedProposal>[] = [
    {
      header: "Job Title",
      accessor: (row) => row.jobTitle,
      cell: (value: unknown) => (
        <div className="font-medium">
          {value && typeof value === "string" ? value : "N/A"}
        </div>
      ),
    },
    {
      header: "Client",
      accessor: (row) => row.clientName,
      cell: (value: unknown) => (
        <div className="text-sm text-muted-foreground">
          {value && typeof value === "string" ? value : "N/A"}
        </div>
      ),
    },
    {
      header: "Bid Amount",
      accessor: (row) => row.bidAmount,
      cell: (value: unknown) => (
        <div className="font-medium">
          ${value && typeof value === "number" ? value.toFixed(2) : "0.00"}
        </div>
      ),
    },
    {
      header: "Status",
      accessor: (row) => row.status,
      cell: (value: unknown) => {
        const status = value as keyof typeof statusVariantMap;
        const variant = statusVariantMap[status] || "outline";
        const icon = statusIconMap[status];
        const statusText = status ? status.charAt(0) + status.slice(1).toLowerCase() : 'Unknown';
        
        return (
          <Badge 
            variant={variant} 
            className="inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full whitespace-nowrap"
          >
            {icon}
            {statusText}
          </Badge>
        );
      },
    },
    {
      header: "Date Submitted",
      accessor: (row) => row.createdAt,
      cell: (value: unknown) => (
        <div className="text-sm text-muted-foreground">
          {value && typeof value === "string"
            ? formatDistanceToNow(new Date(value), { addSuffix: true })
            : "N/A"}
        </div>
      ),
    },
    {
      header: "Actions",
      accessor: (row) => row.id,
      cell: (value: unknown) => (
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/freelancer/proposals/${String(value)}`}>
              <Eye className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      ),
    },
  ];
  console.log("Columns:", columns);

  return (
    <div className="space-y-6">
      <ContentHeader
        title="My Proposals"
        subtitle="View and manage your job proposals"
        actions={[
          {
            label: showFilters ? "Hide Filters" : "Show Filters",
            variant: "outline",
            size: "sm",
            onClick: () => setShowFilters(!showFilters),
            icon: "Filter"
          },
          {
            label: "Browse Jobs",
            variant: "default",
            size: "sm",
            icon: "Briefcase",
            onClick: () => {
              window.location.href = "/freelancer/jobs";
            }
          }
        ]}
      />

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Status</label>
                <Select
                  value={statusFilter}
                  onChange={(e) =>
                    setStatusFilter(e.target.value as StatusFilter)
                  }
                  options={[
                    { value: "", label: "All Statuses" },
                    { value: "PENDING", label: "Pending" },
                    { value: "ACCEPTED", label: "Accepted" },
                    { value: "REJECTED", label: "Rejected" },
                  ]}
                />
              </div>
              <div className="relative">
                <label className="text-sm font-medium mb-1 block">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search proposals..."
                    className="pl-9"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : filteredProposals.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-medium">No proposals found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm || statusFilter
                  ? "Try adjusting your search or filter criteria."
                  : "You haven't submitted any proposals yet."}
              </p>
              {!searchTerm && !statusFilter && (
                <Button className="mt-4" asChild>
                  <Link href="/freelancer/jobs">Browse Jobs</Link>
                </Button>
              )}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table
                columns={columns}
                data={currentProposals}
                emptyState={{
                  title: "No proposals found",
                  description:
                    searchTerm || statusFilter
                      ? "Try adjusting your search or filter criteria."
                      : "You haven't submitted any proposals yet.",
                  action:
                    !searchTerm && !statusFilter ? (
                      <Button className="mt-4" asChild>
                        <Link href="/freelancer/jobs">Browse Jobs</Link>
                      </Button>
                    ) : undefined,
                  icon: "FileText",
                }}
                rowClassName="hover:bg-muted/50"
                keyField="id"
              />
            </div>
          )}

          {!isLoading && filteredProposals.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing <span className="font-medium">{startIndex + 1}</span> to{" "}
                <span className="font-medium">
                  {Math.min(
                    startIndex + itemsPerPage,
                    filteredProposals.length
                  )}
                </span>{" "}
                of{" "}
                <span className="font-medium">{filteredProposals.length}</span>{" "}
                proposals
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((p: number) => Math.max(1, p - 1))
                  }
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((p: number) => Math.min(totalPages, p + 1))
                  }
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MyProposalsPage;
