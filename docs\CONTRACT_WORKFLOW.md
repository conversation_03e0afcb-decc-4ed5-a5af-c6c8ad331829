# Contract Workflow Documentation

## Overview

The contract workflow system manages the complete lifecycle of freelance contracts from creation to payment. It provides a structured process for clients and freelancers to collaborate on projects with clear status transitions and role-based permissions.

## Contract Status Flow

```
DRAFT → PENDING_FREELANCER_ACCEPTANCE → ACTIVE → WORK_SUBMITTED → COMPLETED → PAID
                    ↓                      ↓           ↓             ↓
                CANCELLED              CANCELLED   REVISIONS_   REVISIONS_
                                                  REQUESTED    REQUESTED
                                                      ↓            ↓
                                                 WORK_SUBMITTED → COMPLETED
```

## Status Definitions

### DRAFT
- **Description**: Contract is being prepared by the client
- **Actions**: <PERSON><PERSON> can edit contract details, send to freelancer, or cancel
- **Next Status**: PENDING_FREELANCER_ACCEPTANCE or CANCELLED

### PENDING_FREELANCER_ACCEPTANCE
- **Description**: Contract has been sent to freelancer for review
- **Actions**: <PERSON>lancer can accept or decline the contract
- **Next Status**: ACTIVE (accept) or CANCELLED (decline)

### ACTIVE
- **Description**: Contract is active and work can begin
- **Actions**: Freelancer can submit work, either party can cancel
- **Next Status**: WORK_SUBMITTED or CANCELLED

### WORK_SUBMITTED
- **Description**: Freelancer has submitted completed work for review
- **Actions**: Client can approve work or request revisions
- **Next Status**: COMPLETED (approve) or REVISIONS_REQUESTED

### REVISIONS_REQUESTED
- **Description**: Client has requested changes to the submitted work
- **Actions**: Freelancer can resubmit work, either party can cancel
- **Next Status**: WORK_SUBMITTED or CANCELLED

### COMPLETED
- **Description**: Work has been approved by the client
- **Actions**: Client can process payment or request additional changes
- **Next Status**: PAID or REVISIONS_REQUESTED

### PAID
- **Description**: Payment has been processed (final status)
- **Actions**: No further actions available
- **Next Status**: None (terminal state)

### CANCELLED
- **Description**: Contract has been cancelled (final status)
- **Actions**: No further actions available
- **Next Status**: None (terminal state)

## User Roles and Permissions

### Client Permissions
- **DRAFT**: Edit contract, send to freelancer, cancel
- **PENDING_FREELANCER_ACCEPTANCE**: View status, cancel
- **ACTIVE**: View progress, cancel
- **WORK_SUBMITTED**: Approve work, request revisions
- **REVISIONS_REQUESTED**: View status, cancel
- **COMPLETED**: Process payment, request additional changes
- **PAID**: View final contract
- **CANCELLED**: View cancelled contract

### Freelancer Permissions
- **DRAFT**: No access
- **PENDING_FREELANCER_ACCEPTANCE**: Accept or decline contract
- **ACTIVE**: Submit work, cancel
- **WORK_SUBMITTED**: View status
- **REVISIONS_REQUESTED**: Resubmit work, cancel
- **COMPLETED**: View approved work
- **PAID**: View final contract
- **CANCELLED**: View cancelled contract

## Components

### ContractActions
Main component for contract workflow actions. Displays appropriate buttons based on contract status and user role.

**Props:**
- `contract`: Contract or ExtendedContract object
- `userRole`: UserRole enum (CLIENT or FREELANCER)
- `userId`: Current user's ID
- `onStatusUpdate`: Callback for status changes
- `onJobStatusUpdate`: Callback for job status changes

**Usage:**
```tsx
<ContractActions
  contract={contract}
  userRole={user.role}
  userId={user.id}
  onStatusUpdate={(newStatus) => setContract(prev => ({ ...prev, status: newStatus }))}
  onJobStatusUpdate={(newStatus) => console.log('Job status:', newStatus)}
/>
```

### WorkSubmissionForm
Form component for freelancers to submit completed work.

**Props:**
- `contractId`: ID of the contract
- `onSubmissionSuccess`: Callback when work is submitted successfully

**Features:**
- File upload with S3 integration
- Link submission
- Rich text description
- Validation and error handling

### WorkSubmissionsList
Component to display and manage work submissions.

**Props:**
- `contractId`: ID of the contract
- `userRole`: Current user's role
- `userId`: Current user's ID
- `contractStatus`: Current contract status
- `onStatusUpdate`: Callback for status changes

**Features:**
- Display all submissions for a contract
- Client review actions (approve/request changes)
- File download and link access
- Submission history

### ContractErrorBoundary
Error boundary component for contract workflow error handling.

**Props:**
- `children`: Child components to wrap
- `fallback`: Optional custom error UI
- `onError`: Optional error callback

## API Integration

### Contract Service Methods

#### Status Management
- `updateContractStatus(id, status)`: Update contract status
- `acceptContract(id)`: Accept a pending contract
- `rejectContract(id)`: Reject a pending contract
- `completeContract(id)`: Mark contract as complete

#### Work Submission
- `createWorkSubmission(submission)`: Create new work submission
- `updateWorkSubmission(id, updates)`: Update submission (for reviews)
- `getContractWorkSubmissions(contractId)`: Get all submissions for contract

#### Payment Processing
- `createPayment(contractId, amount)`: Process payment
- `getContractPayments(contractId)`: Get payment history

#### Utility Methods
- `submitWorkAndUpdateStatus(contractId, submission)`: Submit work and update contract status
- `approveWorkAndComplete(contractId, jobId)`: Approve work and complete contract
- `requestRevisions(contractId)`: Request revisions
- `cancelContract(contractId, jobId)`: Cancel contract
- `processPayment(contractId, amount)`: Process payment and update status

## Validation

### Contract Action Validation
The system validates all actions before execution:

```typescript
import { validateContractAction } from '@/utils/contractValidation';

const validation = validateContractAction('accept', {
  contract,
  userRole: UserRole.FREELANCER,
  userId: 'freelancer-id'
});

if (!validation.isValid) {
  console.error(validation.message);
}
```

### Work Submission Validation
Work submissions are validated for:
- Description length (minimum 10 characters)
- At least one attachment or link
- Valid URL format for links

### Status Transition Validation
All status transitions are validated based on:
- Current contract status
- User role
- Business rules

## Error Handling

### Error Types
- **Network Errors**: Connection issues, server unavailable
- **Permission Errors**: Unauthorized actions
- **Validation Errors**: Invalid data or state
- **Business Logic Errors**: Invalid workflow transitions

### Error Recovery
- Automatic retry for network errors
- User-friendly error messages
- Fallback UI components
- Error boundary protection

## Testing

### Unit Tests
- Contract validation functions
- Component behavior
- API service methods
- Error handling

### Integration Tests
- Complete workflow scenarios
- Role-based access control
- Status transition flows

### Test Files
- `src/utils/__tests__/contractValidation.test.ts`
- `src/components/contracts/__tests__/ContractActions.test.tsx`

## Best Practices

### State Management
- Always validate actions before execution
- Use optimistic updates with rollback
- Handle loading states appropriately
- Provide user feedback for all actions

### Error Handling
- Wrap components in error boundaries
- Validate user permissions
- Provide clear error messages
- Log errors for debugging

### Performance
- Lazy load components when possible
- Optimize file uploads
- Cache contract data appropriately
- Use pagination for large lists

### Security
- Validate all user actions server-side
- Implement proper authentication
- Use role-based access control
- Sanitize user inputs

## Implementation Notes

### Database Schema Updates
The following new types were added to the GraphQL schema:
- Enhanced `ContractStatus` enum with workflow states
- `WorkSubmission` type for work deliverables
- `PaymentSchedule` type for payment tracking
- `Deliverable` type for contract deliverables

### File Structure
```
src/
├── components/contracts/
│   ├── ContractActions.tsx          # Main workflow actions
│   ├── WorkSubmissionForm.tsx       # Work submission interface
│   ├── WorkSubmissionsList.tsx      # Submission management
│   ├── ContractErrorBoundary.tsx    # Error handling
│   └── __tests__/                   # Component tests
├── api/contracts/
│   ├── contract.service.ts          # Enhanced service layer
│   ├── contract.api.ts              # API integration
│   ├── contract.mutations.ts        # GraphQL mutations
│   └── contract.queries.ts          # GraphQL queries
├── utils/
│   ├── contractValidation.ts        # Validation utilities
│   └── __tests__/                   # Utility tests
└── types/features/contracts/
    └── contract.types.ts            # Type definitions
```

## Future Enhancements

### Planned Features
- Real-time status updates
- Email notifications
- Contract templates
- Milestone-based payments
- Dispute resolution system
- Contract analytics

### Technical Improvements
- WebSocket integration for real-time updates
- Enhanced file preview capabilities
- Advanced search and filtering
- Mobile app support
- API rate limiting
