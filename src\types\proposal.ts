import { Job } from './job';

export type ProposalStatus =
  | 'PENDING'
  | 'ACCEPTED'
  | 'REJECTED';

export interface JobProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  status: ProposalStatus;
  bidAmount: number;
  createdAt: string;
  updatedAt?: string;
  job?: Job;
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePicture?: string;
  };
}

export interface CreateJobProposalInput {
  jobId: string;
  coverLetter: string;
  bidAmount: number;
}

export interface UpdateJobProposalInput {
  id: string;
  coverLetter?: string;
  bidAmount?: number;
  status?: ProposalStatus;
}
