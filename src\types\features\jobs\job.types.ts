import { ApiResponse, PaginatedResponse } from '@/types/common/api.types';

export enum JobStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum JobType {
  FIXED_PRICE = 'FIXED_PRICE',
  HOURLY = 'HOURLY'
}

export interface Job {
  id: string;
  clientId: string;
  title: string;
  description: string;
  budget: number;
  type: JobType;
  status: JobStatus;
  skills: string[];
  category: string;
  deadline?: string;
  location?: {
    type: string;
    coordinates: [number, number];
    address?: string;
  };
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
  client?: {
    id: string;
    name: string;
    avatar?: string;
    rating?: number;
  };
  proposalsCount?: number;
}

export interface CreateJobDto {
  title: string;
  description: string;
  budget: number;
  type: JobType;
  skills: string[];
  category: string;
  deadline?: string;
  location?: {
    type: string;
    coordinates: [number, number];
    address?: string;
  };
  attachments?: string[];
}

export interface UpdateJobDto extends Partial<CreateJobDto> {
  status?: JobStatus;
}

export type JobsResponse = PaginatedResponse<Job>;
export type JobResponse = ApiResponse<Job>;

export interface JobCardProps {
  job: Job;
  onView?: (job: Job) => void;
  onApply?: (job: Job) => void;
  className?: string;
}

export interface JobFilters {
  query?: string;
  minBudget?: number;
  maxBudget?: number;
  categories?: string[];
  skills?: string[];
  jobType?: JobType;
  status?: JobStatus;
  sortBy?: 'newest' | 'budget_high' | 'budget_low';
}

export interface JobSearchParams {
  query?: string;
  category?: string;
  minBudget?: number;
  maxBudget?: number;
  skills?: string[];
  type?: JobType;
  status?: JobStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
