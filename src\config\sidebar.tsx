import React from 'react';
import { Icon } from '@/components/ui/Icon';

export type UserRole = 'CLIENT' | 'FREELANCER' | 'ADMIN';

export interface SidebarItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  badge?: string | number;
  active?: boolean;
}

const createSidebarItem = (name: string, href: string, icon: React.ReactNode, options: Partial<Omit<SidebarItem, 'name' | 'href' | 'icon'>> = {}): SidebarItem => ({
  name,
  href,
  icon,
  ...options
});

const DashboardIcon = () => (
  <Icon name="LayoutDashboard" size="md" />
);

export const getSidebarItems = (
  userRole: UserRole,
  currentPath: string = '',
  proposalCount?: number
): SidebarItem[] => {
  const isActive = (path: string) => currentPath === path || currentPath.startsWith(`${path}/`);

  const freelancerItems: SidebarItem[] = [
    createSidebarItem(
      'Dashboard',
      '/freelancer/dashboard',
      <DashboardIcon key="dashboard" />,
      { active: isActive('/freelancer/dashboard') }
    ),
    createSidebarItem(
      'Contracts',
      '/contracts',
      <Icon key="contracts" name="FileText" size="md" />,
      { active: isActive('/contracts') }
    ),
    createSidebarItem(
      'Find Jobs',
      '/freelancer/jobs',
      <Icon key="search" name="Search" size="md" />,
      { active: isActive('/freelancer/jobs') }
    ),
    createSidebarItem(
      'My Proposals',
      '/freelancer/proposals',
      <Icon key="proposals" name="FileCheck" size="md" />,
      {
        badge: proposalCount && proposalCount > 0 ? proposalCount : undefined,
        active: isActive('/freelancer/proposals')
      }
    ),
    createSidebarItem(
      'Messages',
      '/freelancer/messages',
      <Icon key="messages" name="MessageSquare" size="md" />,
      { active: isActive('/freelancer/messages') }
    ),
  ];

  const clientItems: SidebarItem[] = [
    createSidebarItem(
      'Dashboard',
      '/client/dashboard',
      <DashboardIcon key="dashboard" />,
      { active: isActive('/client/dashboard') }
    ),
    createSidebarItem(
      'Contracts',
      '/contracts',
      <Icon key="contracts" name="FileText" size="md" />,
      { active: isActive('/contracts') }
    ),
    createSidebarItem(
      'My Jobs',
      '/client/jobs',
      <Icon key="jobs" name="Briefcase" size="md" />,
      { active: isActive('/client/jobs') }
    ),
    createSidebarItem(
      'Proposals',
      '/client/proposals',
      <Icon key="proposals" name="FileCheck" size="md" />,
      { active: isActive('/client/proposals') }
    ),
    createSidebarItem(
      'Messages',
      '/client/messages',
      <Icon key="messages" name="MessageSquare" size="md" />,
      { active: isActive('/client/messages') }
    )
  ];

  const adminItems: SidebarItem[] = [
    createSidebarItem(
      'Dashboard',
      '/admin/dashboard',
      <DashboardIcon key="dashboard" />,
      { active: isActive('/admin/dashboard') }
    ),
    createSidebarItem(
      'Users',
      '/admin/users',
      <Icon name="Users" size="md" />,
      { active: isActive('/admin/users') }
    ),
    createSidebarItem(
      'Jobs',
      '/admin/jobs',
      <Icon name="Briefcase" size="md" />,
      { active: isActive('/admin/jobs') }
    ),
    createSidebarItem(
      'Reports',
      '/admin/reports',
      <Icon name="FileText" size="md" />,
      { active: isActive('/admin/reports') }
    ),
    createSidebarItem(
      'Settings',
      '/admin/settings',
      <Icon name="Settings" size="md" />,
      { active: isActive('/admin/settings') }
    )
  ];

  switch (userRole) {
    case 'CLIENT':
      return clientItems;
    case 'FREELANCER':
      return freelancerItems;
    case 'ADMIN':
      return adminItems;
    default:
      return [];
  }
};
