import { Job } from './job';

export enum ProposalStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED'
}

export interface JobProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  freelancerName?: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate?: number;
  status: ProposalStatus;
  createdAt: string;
  updatedAt?: string;
  job?: Job;
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePicture?: string;
  };
}

export interface CreateJobProposalInput {
  jobId: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate?: number;
}

export interface UpdateJobProposalInput {
  id: string;
  coverLetter?: string;
  bidAmount?: number;
  proposedRate?: number;
  status?: ProposalStatus;
}

export interface JobProposalConnection {
  items: JobProposal[];
  nextToken?: string;
}
