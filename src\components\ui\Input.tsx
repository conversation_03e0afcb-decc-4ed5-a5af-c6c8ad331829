import React from 'react';
import { cn } from '@/lib/utils';

type BaseInputProps = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'prefix'>;

interface InputProps extends BaseInputProps {
  error?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, prefix, suffix, style, ...props }, ref) => {
    const hasPrefix = !!prefix;
    const hasSuffix = !!suffix;

    return (
      <div className="relative flex items-center w-full">
        {prefix && (
          <div className="absolute left-3 text-muted-foreground pointer-events-none">
            {prefix}
          </div>
        )}
        <input
          type={type}
          style={{
            height: '48px',
            paddingLeft: hasPrefix ? '1.5rem' : '1rem',
            paddingRight: hasSuffix ? '1.5rem' : '1rem',
            fontSize: '16px',
            fontWeight: '500',
            borderRadius: '8px',
            border: '2px solid',
            borderColor: error ? 'rgb(220, 38, 38)' : 'var(--input)',
            backgroundColor: 'var(--background)',
            color: 'var(--foreground)',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            transition: 'all 0.2s ease-in-out',
            ...style,
          }}
          className={cn(
            'placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1 focus-visible:border-ring disabled:cursor-not-allowed disabled:opacity-50 hover:border-ring/50',
            error && 'border-destructive focus-visible:ring-destructive focus-visible:border-destructive',
            className
          )}
          ref={ref}
          {...props}
        />
        {suffix && (
          <div className="absolute right-3 text-muted-foreground pointer-events-none">
            {suffix}
          </div>
        )}
      </div>
    );
  }
);
Input.displayName = 'Input';

export { Input, type InputProps };
