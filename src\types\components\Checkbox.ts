import { InputHTMLAttributes } from 'react';

export interface CheckboxProps extends InputHTMLAttributes<HTMLInputElement> {
  /** Label text */
  label?: string;
  /** Optional description */
  description?: string;
  /** Whether the checkbox has an error */
  error?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Whether the checkbox is in an indeterminate state */
  indeterminate?: boolean;
}
