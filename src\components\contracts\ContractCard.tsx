import React from 'react';
import { format } from 'date-fns';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON>, CardDescription, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Contract, ContractStatus } from '@/types/features/contracts/contract.types';
import { ContractType } from '@/types/features/contracts/contract.types';

interface ContractCardProps {
  contract: Contract;
  onView?: () => void;
  onAccept?: () => void;
  onReject?: () => void;
  onComplete?: () => void;
  className?: string;
}

const statusColors: Record<ContractStatus, string> = {
  DRAFT: 'bg-gray-100 text-gray-800',
  PENDING_FREELANCER_ACCEPTANCE: 'bg-orange-100 text-orange-800',
  ACTIVE: 'bg-blue-100 text-blue-800',
  WORK_SUBMITTED: 'bg-purple-100 text-purple-800',
  REVISIONS_REQUESTED: 'bg-amber-100 text-amber-800',
  COMPLETED: 'bg-green-100 text-green-800',
  PAID: 'bg-emerald-100 text-emerald-800',
  CANCELLED: 'bg-red-100 text-red-800',
  DISPUTED: 'bg-yellow-100 text-yellow-800',
};

const typeLabels: Record<ContractType, string> = {
  FIXED_PRICE: 'Fixed Price',
  HOURLY: 'Hourly Rate',
};

export const ContractCard: React.FC<ContractCardProps> = ({
  contract,
  onView,
  onAccept,
  onReject,
  onComplete,
  className = '',
}) => {
  const {
    title,
    status,
    type,
    budget,
    startDate,
    endDate,
  } = contract;

  const formattedStartDate = format(new Date(startDate), 'MMM d, yyyy');
  const formattedEndDate = endDate ? format(new Date(endDate), 'MMM d, yyyy') : 'Ongoing';

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold line-clamp-1">{title}</CardTitle>
            <CardDescription className="mt-1">
              {formattedStartDate} - {formattedEndDate}
            </CardDescription>
          </div>
          <Badge className={statusColors[status]}>{status.replace('_', ' ')}</Badge>
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Type</p>
            <p className="font-medium">{typeLabels[type]}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Budget</p>
            <p className="font-medium">${budget.toLocaleString()}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <Button variant="outline" size="sm" onClick={onView}>
          View Details
        </Button>
        <div className="space-x-2">
          {status === 'DRAFT' && onReject && (
            <Button variant="outline" size="sm" onClick={onReject}>
              Reject
            </Button>
          )}
          {status === 'DRAFT' && onAccept && (
            <Button size="sm" onClick={onAccept}>
              Accept
            </Button>
          )}
          {status === 'ACTIVE' && onComplete && (
            <Button size="sm" onClick={onComplete}>
              Mark as Complete
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ContractCard;
