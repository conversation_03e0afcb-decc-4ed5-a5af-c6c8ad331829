'use client';

import React, { createContext, useContext, ReactNode, useCallback, useState, useMemo } from 'react';
import { MessagingUser, Conversation } from '@/components/messaging/types';
import messageService from '@/api/messaging/message.service';

export interface MessagingContextType {
  isDrawerOpen: boolean;
  openMessagingDrawer: () => void;
  closeMessagingDrawer: () => void;
  toggleMessagingDrawer: () => void;
  currentUser: MessagingUser | null;
  setCurrentUser: (user: MessagingUser) => void;
  conversations: Conversation[];
  setConversations: (conversations: Conversation[]) => void;
  activeConversation: string | null;
  setActiveConversation: (conversationId: string | null) => void;
  sendMessage: (content: string) => Promise<void>;
  loadMoreMessages: (conversationId: string, before: Date) => Promise<any[]>;
  onFileUpload?: (file: File) => Promise<string>;
  
  startThreadFromApplication: (jobId: string, freelancerId: string, clientId: string, initialMessage?: string) => Promise<string>;
  startThreadFromInvitation: (jobId: string, clientId: string, freelancerId: string, initialMessage?: string) => Promise<string>;
  validateMessagingPermission: (targetUserId: string, jobId?: string) => Promise<{ canMessage: boolean; reason?: string }>;
  isMessagingLoading: boolean;
  messagingError: Error | null;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const MessagingProvider: React.FC<{
  children: ReactNode;
  currentUser: MessagingUser;
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<any[]>;
  onFileUpload?: (file: File) => Promise<string>;
}> = ({ 
  children, 
  currentUser: initialUser, 
  initialConversations = [],
  onSendMessage: externalSendMessage,
  onLoadMoreMessages: externalLoadMoreMessages,
  onFileUpload: externalFileUpload,
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<MessagingUser>(initialUser);
  const [conversations, setConversations] = useState<Conversation[]>(initialConversations);
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [isMessagingLoading, setIsMessagingLoading] = useState(false);
  const [messagingError, setMessagingError] = useState<Error | null>(null);

  const openMessagingDrawer = useCallback(() => {
    setIsDrawerOpen(true);
    document.body.style.overflow = 'hidden';
  }, []);

  const closeMessagingDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    document.body.style.overflow = '';
  }, []);

  const toggleMessagingDrawer = useCallback(() => {
    setIsDrawerOpen(prev => {
      const newState = !prev;
      document.body.style.overflow = newState ? 'hidden' : '';
      return newState;
    });
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!activeConversation || !currentUser) return;
    
    try {
      await externalSendMessage(activeConversation, content);
      
      setConversations(prev => 
        prev.map(conv => {
          if (conv.id !== activeConversation) return conv;
          
          const now = new Date();
          const otherParticipant = conv.participants.find(p => p.id !== currentUser.id);
          
          if (!otherParticipant) return conv;
          
          const newMessage = {
            id: `temp-${Date.now()}`,
            content,
            senderId: currentUser.id,
            receiverId: otherParticipant.id,
            conversationId: conv.id,
            createdAt: now.toISOString(),
            updatedAt: now.toISOString(),
            status: 'sent' as const,
            type: 'text' as const,
            sender: currentUser,
            receiver: otherParticipant,
          };
          
          const updatedConversation: Conversation = {
            ...conv,
            updatedAt: now,
            lastMessage: {
              id: newMessage.id,
              content: newMessage.content,
              senderId: newMessage.senderId,
              receiverId: newMessage.receiverId,
              conversationId: newMessage.conversationId,
              createdAt: newMessage.createdAt,
              updatedAt: newMessage.updatedAt,
              status: newMessage.status,
              type: newMessage.type,
              sender: currentUser,
              receiver: otherParticipant
            },
            // @ts-ignore - We know these properties exist in the Conversation type
            messages: [...(conv.messages || []), newMessage],
          };
          
          return updatedConversation;
        })
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }, [activeConversation, currentUser, externalSendMessage]);

  const loadMoreMessages = useCallback(async (conversationId: string, before: Date) => {
    const messages = await externalLoadMoreMessages(conversationId, before);
    return messages;
  }, [externalLoadMoreMessages]);

  React.useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const startThreadFromApplication = useCallback(async (
    jobId: string,
    freelancerId: string,
    clientId: string,
    initialMessage?: string
  ) => {
    if (!currentUser) {
      throw new Error('User must be authenticated to start a conversation');
    }

    setIsMessagingLoading(true);
    setMessagingError(null);

    try {
      const conversationId = await messageService.createConversation(
        jobId,
        clientId,
        freelancerId
      );
      
      if (initialMessage) {
        await messageService.sendMessage(
          conversationId,
          currentUser.id,
          freelancerId === currentUser.id ? clientId : freelancerId,
          initialMessage
        );
      }
      return conversationId;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to start conversation');
      setMessagingError(error);
      throw error;
    } finally {
      setIsMessagingLoading(false);
    }
  }, [currentUser]);

  const startThreadFromInvitation = useCallback(async (
    jobId: string,
    clientId: string,
    freelancerId: string,
    initialMessage?: string
  ) => {
    if (!currentUser) {
      throw new Error('User must be authenticated to send an invitation');
    }

    setIsMessagingLoading(true);
    setMessagingError(null);

    try {
      const conversationId = await messageService.createConversation(
        jobId,
        clientId,
        freelancerId
      );
      
      if (initialMessage) {
        await messageService.sendMessage(
          conversationId,
          currentUser.id,
          freelancerId === currentUser.id ? clientId : freelancerId,
          initialMessage
        );
      }
      
      return conversationId;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to send invitation');
      setMessagingError(error);
      throw error;
    } finally {
      setIsMessagingLoading(false);
    }
  }, [currentUser]);

  const validateMessagingPermission = useCallback(async () => {
    if (!currentUser) {
      return { canMessage: false, reason: 'You must be logged in to message users' };
    }
    
    return { canMessage: true, reason: '' };
  }, [currentUser]);

  const contextValue = useMemo(() => ({
    isDrawerOpen,
    openMessagingDrawer,
    closeMessagingDrawer,
    toggleMessagingDrawer,
    currentUser,
    setCurrentUser,
    conversations,
    setConversations,
    activeConversation,
    setActiveConversation,
    sendMessage,
    loadMoreMessages,
    onFileUpload: externalFileUpload,
    startThreadFromApplication,
    startThreadFromInvitation,
    validateMessagingPermission,
    isMessagingLoading,
    messagingError,
  }), [
    isDrawerOpen,
    openMessagingDrawer,
    closeMessagingDrawer,
    toggleMessagingDrawer,
    currentUser,
    conversations,
    activeConversation,
    sendMessage,
    loadMoreMessages,
    externalFileUpload,
    startThreadFromApplication,
    startThreadFromInvitation,
    validateMessagingPermission,
    isMessagingLoading,
    messagingError,
  ]);

  return (
    <MessagingContext.Provider value={contextValue}>
      {children}
    </MessagingContext.Provider>
  );
};

export const useMessaging = (): MessagingContextType => {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};
