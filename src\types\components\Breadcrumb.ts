import { ReactNode } from 'react';

export interface BreadcrumbItem {
  /** Display text for the breadcrumb item */
  label: string | ReactNode;
  /** URL for the breadcrumb link */
  href?: string;
  /** Whether this is the current page */
  current?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Icon to display before the label */
  icon?: ReactNode;
  /** Whether the breadcrumb is clickable */
  isClickable?: boolean;
  /** Callback when the breadcrumb is clicked */
  onClick?: (event: React.MouseEvent<HTMLAnchorElement | HTMLSpanElement>) => void;
  /** Additional props for the breadcrumb item */
  itemProps?: React.HTMLAttributes<HTMLElement>;
}

export interface BreadcrumbProps {
  /** Array of breadcrumb items */
  items: BreadcrumbItem[];
  /** Custom separator between breadcrumb items */
  separator?: ReactNode;
  /** Additional CSS classes for the breadcrumb container */
  className?: string;
  /** Additional CSS classes for the breadcrumb list */
  listClassName?: string;
  /** Additional CSS classes for breadcrumb items */
  itemClassName?: string;
  /** Additional CSS classes for breadcrumb links */
  linkClassName?: string;
  /** Additional CSS classes for the current breadcrumb item */
  currentItemClassName?: string;
  /** Additional CSS classes for the separator */
  separatorClassName?: string;
  /** Whether to show the home breadcrumb item */
  showHome?: boolean;
  /** Custom home breadcrumb item */
  homeItem?: BreadcrumbItem;
  /** Maximum number of breadcrumb items to show before collapsing */
  maxItems?: number;
  /** Text to show when breadcrumbs are collapsed */
  itemsBeforeCollapse?: number;
  /** Text to show when breadcrumbs are collapsed */
  itemsAfterCollapse?: number;
  /** Custom renderer for collapsed items */
  onRenderCollapsed?: (collapsedItems: BreadcrumbItem[]) => ReactNode;
  /** Custom renderer for the breadcrumb item */
  renderItem?: (item: BreadcrumbItem, isLast: boolean) => ReactNode;
  /** Additional props for the breadcrumb container */
  containerProps?: React.HTMLAttributes<HTMLElement>;
  /** Additional props for the breadcrumb list */
  listProps?: React.OlHTMLAttributes<HTMLOListElement>;
  /** Additional props for the breadcrumb item */
  itemProps?: React.LiHTMLAttributes<HTMLLIElement>;
  /** Additional props for the breadcrumb link */
  linkProps?: React.AnchorHTMLAttributes<HTMLAnchorElement>;
  /** Additional props for the breadcrumb separator */
  separatorProps?: React.HTMLAttributes<HTMLSpanElement>;
}
