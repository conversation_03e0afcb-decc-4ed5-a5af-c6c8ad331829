'use client';

import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight, Dot } from 'lucide-react';
import Image from 'next/image';
import { Badge } from '@/components/ui/Badge';
import { Skeleton } from '@/components/ui/Skeleton';
import { getProfilePhotoUrl } from '@/utils/profilePhoto';
import { 
  Conversation as UIConversation, 
  UserConversationGroup, 
  JobConversation,
} from '@/types/messaging';
import { useState, useMemo } from 'react';

export type Conversation = UIConversation;

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (id: string) => void;
  currentUserId: string;
  currentUserRole: 'CLIENT' | 'FREELANCER';
  loading?: boolean;
  emptyState?: React.ReactNode;
}

function groupConversationsByUser(
  conversations: Conversation[], 
  currentUserId: string
): UserConversationGroup[] {
  const userMap = new Map<string, UserConversationGroup>();
  
  const sortedConversations = [...conversations].sort((a, b) => {
    const timeA = a.lastMessage?.createdAt ? new Date(a.lastMessage.createdAt).getTime() : 0;
    const timeB = b.lastMessage?.createdAt ? new Date(b.lastMessage.createdAt).getTime() : 0;
    return timeB - timeA;
  });
  
  const userJobMap = new Map<string, Set<string>>();
  
  sortedConversations.forEach(conversation => {
    const isCurrentUserClient = conversation.clientId === currentUserId;
    const isCurrentUserFreelancer = conversation.freelancerId === currentUserId;
    
    if (!isCurrentUserClient && !isCurrentUserFreelancer) return;
    
    const otherUser = isCurrentUserClient ? conversation.freelancer : conversation.client;
    
    if (!otherUser) {
      console.warn('Could not determine other user in conversation', conversation);
      return;
    }
    
    if (!userMap.has(otherUser.id)) {
      userMap.set(otherUser.id, {
        userId: otherUser.id,
        userName: otherUser.name || 'Unknown User',
        userAvatar: otherUser.avatar || otherUser.profilePhoto,
        userRole: isCurrentUserClient ? 'FREELANCER' : 'CLIENT',
        isOnline: otherUser.isOnline || false,
        jobs: [],
        isExpanded: true
      });
      
      userJobMap.set(otherUser.id, new Set());
    }
    
    const userGroup = userMap.get(otherUser.id)!;
    const userJobs = userJobMap.get(otherUser.id)!;
    
    if (!userJobs.has(conversation.jobId)) {
      userJobs.add(conversation.jobId);
      
      const mostRecentJobConversation = sortedConversations.find(c => {
        const isSameJob = c.jobId === conversation.jobId;
        const isWithSameUser = (c.clientId === otherUser.id || c.freelancerId === otherUser.id);
        const isWithCurrentUser = (c.clientId === currentUserId || c.freelancerId === currentUserId);
        return isSameJob && isWithSameUser && isWithCurrentUser;
      });
      
      if (mostRecentJobConversation) {
        userGroup.jobs.push({
          id: mostRecentJobConversation.id,
          jobId: mostRecentJobConversation.jobId,
          jobTitle: mostRecentJobConversation.job?.title || 'Untitled Job',
          lastMessage: mostRecentJobConversation.lastMessage,
          unreadCount: mostRecentJobConversation.unreadCount || 0,
          isActive: mostRecentJobConversation.id === (userGroup.jobs[0]?.id || mostRecentJobConversation.id)
        });
      }
    }
  });
  
  return Array.from(userMap.values()).map(group => ({
    ...group,
    jobs: group.jobs.sort((a, b) => {
      const timeA = a.lastMessage?.createdAt ? new Date(a.lastMessage.createdAt).getTime() : 0;
      const timeB = b.lastMessage?.createdAt ? new Date(b.lastMessage.createdAt).getTime() : 0;
      return timeB - timeA;
    })
  }));
}

export function ConversationList({
  conversations,
  selectedConversationId,
  onSelectConversation,
  currentUserId,
  loading = false,
  emptyState,
}: ConversationListProps) {
  const [expandedUsers, setExpandedUsers] = useState<Record<string, boolean>>({});
  
  const userGroups = useMemo(() => 
    groupConversationsByUser(conversations, currentUserId)
  , [conversations, currentUserId]);

  const toggleUserExpanded = (userId: string) => {
    setExpandedUsers(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3 rounded-lg">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-3 w-full" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center text-muted-foreground">
        {emptyState || (
          <>
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-muted-foreground"
              >
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-1">No conversations yet</h3>
            <p className="text-sm">Start a conversation to see it here</p>
          </>
        )}
      </div>
    );
  }

  const renderUserAvatar = (user: { userAvatar?: string | null; userName?: string | null }) => {
    const avatarUrl = user.userAvatar ? getProfilePhotoUrl(user.userAvatar) : undefined;
    const initials = user.userName?.trim() ? user.userName.trim().charAt(0).toUpperCase() : '?';
    
    return (
      <div className="relative">
        <div className="w-10 h-10 rounded-full bg-muted-foreground/10 flex items-center justify-center overflow-hidden">
          {avatarUrl ? (
            <>
              <Image
                src={avatarUrl}
                alt={user.userName || 'User'}
                width={40}
                height={40}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) {
                    fallback.classList.remove('hidden');
                  }
                }}
                unoptimized={avatarUrl.startsWith('data:')} // Disable optimization for data URLs
              />
              <span className="text-sm font-medium text-muted-foreground hidden">
                {initials}
              </span>
            </>
          ) : (
            <span className="text-sm font-medium text-muted-foreground">
              {user.userName?.charAt(0).toUpperCase() || '?'}
            </span>
          )}
        </div>
        <span className="absolute bottom-0 right-0 w-2.5 h-2.5 rounded-full bg-green-500 border-2 border-background"></span>
      </div>
    );
  };

  const renderJobItem = (job: JobConversation) => {
    const isActive = selectedConversationId === job.id;
    
    return (
      <button
        key={job.id}
        className={cn(
          'w-full text-left pl-14 pr-3 py-2 hover:bg-muted/30 transition-colors',
          isActive ? 'bg-muted/30' : '',
          (job.unreadCount ?? 0) > 0 ? 'font-medium' : ''
        )}
        onClick={(e) => {
          e.stopPropagation();
          onSelectConversation(job.id);
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center truncate">
            <Dot className="flex-shrink-0 text-muted-foreground" />
            <span className="truncate">{job.jobTitle}</span>
          </div>
          {(job.unreadCount ?? 0) > 0 && (
            <Badge variant="default" className="ml-2 flex-shrink-0">
              {job.unreadCount}
            </Badge>
          )}
        </div>
        {job.lastMessage && (
          <p className="text-xs text-muted-foreground truncate pl-4 mt-1">
            {job.lastMessage.content}
          </p>
        )}
      </button>
    );
  };

  return (
    <div className="divide-y divide-border overflow-y-auto">
      {userGroups.map((userGroup) => {
        const isExpanded = expandedUsers[userGroup.userId] !== false; // Default to expanded
        const hasUnread = userGroup.jobs.some(job => (job.unreadCount ?? 0) > 0);
        
        return (
          <div key={userGroup.userId} className="border-b border-border">
            <button
              className={cn(
                'w-full text-left p-4 flex items-center justify-between hover:bg-muted/30 transition-colors',
                hasUnread && 'bg-primary/5'
              )}
              onClick={() => toggleUserExpanded(userGroup.userId)}
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                {renderUserAvatar(userGroup)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-foreground truncate">
                      {userGroup.userName}
                    </h3>
                    <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                      {userGroup.jobs.length} {userGroup.jobs.length === 1 ? 'project' : 'projects'}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground truncate">
                    {userGroup.userRole.toLowerCase()}
                  </p>
                </div>
              </div>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              ) : (
                <ChevronRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              )}
            </button>
            
            {isExpanded && userGroup.jobs.length > 0 && (
              <div className="pl-4 pb-2 space-y-1">
                {userGroup.jobs.map(job => renderJobItem(job))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
