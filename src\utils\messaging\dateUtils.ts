import { format, isValid } from 'date-fns';

export const safeFormatDate = (dateString: string | Date): string => {
  try {
    const date = new Date(dateString);
    if (!isValid(date)) {
      console.error('Invalid date:', dateString);
      return 'Just now';
    }
    return format(date, 'MMMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Just now';
  }
};

export const shouldShowDate = (currentDate: Date, prevDate?: Date): boolean => {
  if (!prevDate) return true;
  try {
    return currentDate.toDateString() !== prevDate.toDateString();
  } catch (error) {
    console.error('Error comparing dates:', error);
    return true;
  }
};

export const formatMessageTime = (dateString: string | Date): string => {
  try {
    const date = new Date(dateString);
    if (!isValid(date)) return '';
    return format(date, 'h:mm a');
  } catch (error) {
    console.error('Error formatting message time:', error);
    return '';
  }
};
