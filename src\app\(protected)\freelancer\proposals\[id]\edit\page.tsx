'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { proposalService } from '@/api/proposals/proposal.service';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { toast } from 'react-hot-toast';
import { ProposalSubmissionForm } from '@/components/proposals/ProposalSubmissionForm';
import Link from 'next/link';
import { JobProposal as BaseJobProposal, ProposalStatus } from '@/types/proposal.types';

interface JobProposal extends Omit<BaseJobProposal, 'job'> {
  job?: {
    id: string;
    budget: number;
  };
  updatedAt: string;
  bidAmount: number;
  coverLetter: string;
  status: ProposalStatus;
}

export default function EditProposalPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [proposal, setProposal] = useState<JobProposal | null>(null);
  
  const mapToJobProposal = (data: any): JobProposal => ({
    ...data,
    updatedAt: data.updatedAt || new Date().toISOString(),
    bidAmount: data.bidAmount || 0,
    coverLetter: data.coverLetter || '',
    status: data.status as ProposalStatus,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const proposalId = params.id as string;

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    const fetchProposal = async () => {
      try {
        const data = await proposalService.getProposalById(proposalId);
        if (data) {
          setProposal(mapToJobProposal(data));
        } else {
          setError('Proposal not found');
          toast.error('Could not find the requested proposal');
          router.push('/freelancer/proposals');
        }
      } catch (err) {
        console.error('Error fetching proposal:', err);
        setError('Failed to load proposal');
        toast.error('Failed to load proposal details');
        router.push('/freelancer/proposals');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProposal();
  }, [proposalId, router, isAuthenticated]);

  const handleSubmit = async (formData: { coverLetter: string; bidAmount: number }) => {
    if (!proposal) return;
    
    try {
      setIsSubmitting(true);
      const updateData = {
        id: proposal.id,
        coverLetter: formData.coverLetter,
        bidAmount: formData.bidAmount,
      } as const;
      await proposalService.updateProposal(proposal.id, updateData);
      
      toast.success('Proposal updated successfully!');
      router.push(`/freelancer/proposals/${proposal.id}`);
    } catch (error) {
      console.error('Error updating proposal:', error);
      toast.error('Failed to update proposal. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <Icon name="AlertCircle" size="xl" className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">
              {error || 'Proposal not found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              The proposal you`&#39;re trying to edit doesn`&#39;t exist or you don`&#39;t have permission to edit it.
            </p>
            <Button asChild>
              <Link href="/freelancer/proposals">
                View All Proposals
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (proposal.status !== 'PENDING') {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back to Proposal
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <Icon name="AlertCircle" size="xl" className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">
              Cannot Edit Proposal
            </h3>
            <p className="text-muted-foreground mb-4">
              This proposal can no longer be edited because it has been {proposal.status.toLowerCase()}.
            </p>
            <Button asChild>
              <Link href={`/freelancer/proposals/${proposal.id}`}>
                View Proposal
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.back()}>
          <Icon name="ArrowLeft" size="sm" className="mr-2" />
          Back to Proposal
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Edit" size="md" />
            Edit Proposal
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ProposalSubmissionForm
            jobId={proposal.jobId}
            jobBudget={proposal.job?.budget}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            initialValues={{
              coverLetter: proposal.coverLetter,
              bidAmount: proposal.bidAmount.toString(),
            }}
            submitButtonText="Update Proposal"
          />
        </CardContent>
      </Card>
    </div>
  );
}
