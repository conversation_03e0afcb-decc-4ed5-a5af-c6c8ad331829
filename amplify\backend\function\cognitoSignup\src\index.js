const {
  CognitoIdentityProviderClient,
  SignUpCommand,
} = require("@aws-sdk/client-cognito-identity-provider");

const client = new CognitoIdentityProviderClient({ region: process.env.REGION });

exports.handler = async (event) => {
  try {
    const body = JSON.parse(event.body);
    const { email, password, role } = body;

    if (!email || !password || !role) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "Missing required fields: email, password, role",
          error: "MISSING_FIELDS"
        }),
      };
    }

    const signUpResult = await client.send(
      new SignUpCommand({
        ClientId: process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLWEBCLIENTID,
        Username: email,
        Password: password,
        UserAttributes: [
          { Name: "email", Value: email },
          { Name: "name", Value: body.name || email.split('@')[0] },
          { Name: "custom:role", Value: role },
        ],
      })
    );

    console.log('SignUp result:', signUpResult);

    return {
      statusCode: 201,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: "User created successfully. Please check your email for verification code.",
        email: email,
        role: role,
        requiresVerification: true
      }),
    };
  } catch (err) {
    console.error("Signup error:", err);

    if (err.name === 'UsernameExistsException') {
      return {
        statusCode: 409,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "User account already exists",
          error: "ACCOUNT_EXISTS",
          email: JSON.parse(event.body).email
        }),
      };
    }

    if (err.name === 'InvalidPasswordException') {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "Password does not meet requirements",
          error: "INVALID_PASSWORD"
        }),
      };
    }

    if (err.name === 'InvalidParameterException') {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "Invalid parameters provided",
          error: "INVALID_PARAMETERS"
        }),
      };
    }

    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: "Signup failed",
        error: "INTERNAL_ERROR",
        details: err.message
      }),
    };
  }
};
