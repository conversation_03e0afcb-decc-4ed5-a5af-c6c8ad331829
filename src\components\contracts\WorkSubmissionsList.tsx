"use client";

import React, { useState, useEffect, useCallback } from "react";
import { format } from "date-fns";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { useToast } from "@/components/ui/toast";
import {
  FileText,
  ExternalLink,
  Download,
  ThumbsUp,
  MessageSquare,
  Clock,
} from "lucide-react";
import {
  WorkSubmission,
  DeliverableStatus,
  ContractStatus,
} from "@/types/features/contracts/contract.types";
import { UserRole } from "@/types/features/auth/auth.types";
import contractService from "@/api/contracts/contract.service";

interface WorkSubmissionsListProps {
  contractId: string;
  userRole: UserRole;
  userId: string;
  contractStatus: ContractStatus;
  onStatusUpdate?: (newStatus: ContractStatus) => void;
  className?: string;
}

const statusColors: Record<DeliverableStatus, string> = {
  PENDING: "bg-gray-100 text-gray-800",
  SUBMITTED: "bg-blue-100 text-blue-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
};

const statusLabels: Record<DeliverableStatus, string> = {
  PENDING: "Pending",
  SUBMITTED: "Submitted",
  APPROVED: "Approved",
  REJECTED: "Rejected",
};

export const WorkSubmissionsList: React.FC<WorkSubmissionsListProps> = ({
  contractId,
  userRole,
  contractStatus,
  onStatusUpdate,
  className = "",
}) => {
  const [submissions, setSubmissions] = useState<WorkSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const { showToast } = useToast();

  const isClient = userRole === UserRole.CLIENT;
  const isFreelancer = userRole === UserRole.FREELANCER;

  const fetchSubmissions = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await contractService.getContractWorkSubmissions(contractId);
      setSubmissions(data);
    } catch (error) {
      console.error("Error fetching work submissions:", error);
      showToast("Error", {
        description: "Failed to load work submissions.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [contractId, showToast]);

  useEffect(() => {
    fetchSubmissions();
  }, [fetchSubmissions]);

  const handleApproveSubmission = async (submissionId: string) => {
    try {
      setIsUpdating(true);

      await contractService.updateWorkSubmission(submissionId, {
        status: DeliverableStatus.APPROVED,
        reviewNotes: "Work approved by client",
      });

      await contractService.updateContractStatus(
        contractId,
        ContractStatus.COMPLETED
      );

      await fetchSubmissions();

      onStatusUpdate?.(ContractStatus.COMPLETED);

      showToast("Success", {
        description: "Work approved successfully! Contract is now completed.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error approving work:", error);
      showToast("Error", {
        description: "Failed to approve work. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRequestRevisions = async (submissionId: string) => {
    try {
      setIsUpdating(true);

      await contractService.updateWorkSubmission(submissionId, {
        status: DeliverableStatus.REJECTED,
        reviewNotes: "Revisions requested by client",
      });

      await contractService.updateContractStatus(
        contractId,
        ContractStatus.REVISIONS_REQUESTED
      );

      await fetchSubmissions();

      onStatusUpdate?.(ContractStatus.REVISIONS_REQUESTED);

      showToast("Success", {
        description: "Revision request sent to freelancer.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error requesting revisions:", error);
      showToast("Error", {
        description: "Failed to request revisions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getFileName = (url: string) => {
    return url.split("/").pop() || "Unknown file";
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (submissions.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Work Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">No work submissions yet.</p>
            {isFreelancer && contractStatus === ContractStatus.ACTIVE && (
              <p className="text-sm text-gray-400 mt-2">
                Use the form above to submit your work.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Work Submissions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {submissions.map((submission) => (
          <div key={submission.id} className="border rounded-lg p-4 space-y-4">
            {/* Submission Header */}
            <div className="flex items-start justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className={statusColors[submission.status]}>
                    {statusLabels[submission.status]}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    <Clock className="inline h-4 w-4 mr-1" />
                    Submitted{" "}
                    {format(
                      new Date(submission.submittedAt),
                      "MMM d, yyyy h:mm a"
                    )}
                  </span>
                </div>
                {submission.reviewedAt && (
                  <p className="text-sm text-gray-500">
                    Reviewed{" "}
                    {format(
                      new Date(submission.reviewedAt),
                      "MMM d, yyyy h:mm a"
                    )}
                  </p>
                )}
              </div>

              {/* Action Buttons for Client */}
              {isClient &&
                submission.status === DeliverableStatus.SUBMITTED && (
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleApproveSubmission(submission.id)}
                      disabled={isUpdating}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <ThumbsUp className="mr-2 h-4 w-4" />
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRequestRevisions(submission.id)}
                      disabled={isUpdating}
                    >
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Request Changes
                    </Button>
                  </div>
                )}
            </div>

            {/* Description */}
            <div>
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-gray-700 whitespace-pre-wrap">
                {submission.description}
              </p>
            </div>

            {/* Attachments */}
            {submission.attachments && submission.attachments.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Attachments</h4>
                <div className="space-y-2">
                  {submission.attachments.map((attachment, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          {getFileName(attachment)}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(attachment, "_blank")}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Links */}
            {submission.links && submission.links.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Links</h4>
                <div className="space-y-2">
                  {submission.links.map((link, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-gray-500" />
                      <a
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm break-all"
                      >
                        {link}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Review Notes */}
            {submission.reviewNotes && (
              <div>
                <h4 className="font-medium mb-2">Review Notes</h4>
                <p className="text-gray-700 bg-gray-50 p-3 rounded">
                  {submission.reviewNotes}
                </p>
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default WorkSubmissionsList;
