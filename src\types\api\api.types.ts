import { Job, JobsResponse, JobResponse } from '../features/jobs/job.types';
import { Proposal, ProposalsResponse, ProposalResponse } from '../features/proposals/proposal.types';
import { AuthResponse } from '../features/auth/auth.types';
import { Conversation, Message, MessagesResponse } from '../features/messaging/messaging.types';

export interface SuccessResponse {
  success: boolean;
  message?: string;
}

export interface FileUploadResponse {
  url: string;
  key: string;
  name: string;
  size: number;
  type: string;
}

export interface SearchResponse<T> {
  items: T[];
  total: number;
  page: number;
  totalPages: number;
}

export type {
  Job,
  JobsResponse,
  JobResponse,
  Proposal,
  ProposalsResponse,
  ProposalResponse,
  AuthResponse,
  Conversation,
  Message,
  MessagesResponse
};

export enum ApiErrorCode {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

export interface ApiErrorResponse {
  code: ApiErrorCode;
  message: string;
  details?: Record<string, string[]>;
  timestamp?: string;
  path?: string;
}

export interface ApiRequestOptions<T = unknown> {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: T;
  headers?: Record<string, string>;
  params?: Record<string, string | number | boolean | undefined>;
  signal?: AbortSignal;
  cache?: RequestCache;
  next?: {
    revalidate?: number | false;
    tags?: string[];
  };
}

export interface ApiClient {
  request: <T>(endpoint: string, options?: ApiRequestOptions) => Promise<T>;
  get: <T>(endpoint: string, options?: Omit<ApiRequestOptions, 'method' | 'body'>) => Promise<T>;
  post: <T, D = unknown>(
    endpoint: string,
    data: D,
    options?: Omit<ApiRequestOptions<D>, 'method' | 'body'>
  ) => Promise<T>;
  put: <T, D = unknown>(
    endpoint: string,
    data: D,
    options?: Omit<ApiRequestOptions<D>, 'method' | 'body'>
  ) => Promise<T>;
  delete: <T>(endpoint: string, options?: Omit<ApiRequestOptions, 'method' | 'body'>) => Promise<T>;
}
