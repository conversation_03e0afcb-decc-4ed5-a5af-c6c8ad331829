"use client";
import React, { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";

export default function RouteGuard({
  children,
  role,
}: {
  children: React.ReactNode;
  role?: "CLIENT" | "FREELANCER";
}) {
  const { loading, user, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (loading) return;

    if (!isAuthenticated || !user) {
      if (pathname !== "/login") {
        sessionStorage.setItem("redirectAfterLogin", pathname);
      }
      router.replace("/login");
      return;
    }

    const userRole = user.attributes?.["custom:role"] as
      | "CLIENT"
      | "FREELANCER"
      | undefined;

    if (role && userRole !== role) {
      const redirectPath =
        userRole === "CLIENT"
          ? "/client/dashboard"
          : userRole === "FREELANCER"
          ? "/freelancer/dashboard"
          : "/";

      if (pathname !== redirectPath) {
        router.replace(redirectPath);
        return;
      }
    }

  }, [loading, user, isAuthenticated, role, router, pathname]);

  if (loading || !isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        Loading...
      </div>
    );
  }

  const userRole = user.attributes?.["custom:role"] as
    | "CLIENT"
    | "FREELANCER"
    | undefined;

  if (role && userRole !== role) {
    return null;
  }

  return <>{children}</>;
}
