'use client';

import { usePathname } from 'next/navigation';
import { useSidebarMessaging } from '@/hooks/useSidebarMessaging';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { MessageSquare } from 'lucide-react';

export function MessagingSidebarItem() {
  const pathname = usePathname();
  const { handleMessagesClick, isMessagingDrawerOpen } = useSidebarMessaging();
  
  const isMessagesPage = pathname.startsWith('/messages');
  
  const isActive = isMessagesPage || isMessagingDrawerOpen;

  return (
    <Button
      variant="ghost"
      onClick={handleMessagesClick}
      className={cn(
        'w-full justify-start gap-3',
        isActive
          ? 'bg-accent text-accent-foreground hover:bg-accent/90'
          : 'hover:bg-accent/50 hover:text-accent-foreground',
        'transition-colors duration-200',
        'relative',
        'group',
        'text-sm font-medium',
        'px-3 py-2',
        'rounded-md',
        'flex items-center',
        'h-10',
      )}
    >
      <MessageSquare 
        className={cn(
          'h-4 w-4',
          isActive ? 'text-accent-foreground' : 'text-muted-foreground',
          'group-hover:text-accent-foreground',
          'transition-colors duration-200',
        )} 
      />
      <span className={cn(
        'transition-all duration-200',
        'whitespace-nowrap',
        'overflow-hidden',
        'text-left',
        'flex-1',
      )}>
        Messages
      </span>
      
      {/* Unread message badge */}
      <div className={cn(
        'absolute right-2 top-1/2 -translate-y-1/2',
        'h-2 w-2 rounded-full',
        'bg-primary',
        'transition-opacity duration-200',
        isActive ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
        'flex items-center justify-center',
        'text-[10px] font-bold text-primary-foreground',
      )}>
        {/* You can add a number here if you want to show unread count */}
      </div>
    </Button>
  );
}
