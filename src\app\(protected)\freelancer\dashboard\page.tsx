"use client";
import React from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Icon,
} from "@/components/ui";
import { AnimateOnScroll } from "@/components/common/AnimateOnScroll";
import { animations } from "@/utils/animations";

export default function FreelancerDashboard() {
  const { user } = useAuth();

  return (
    <div className="space-y-8">
      <AnimateOnScroll>
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">
            Freelancer Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.attributes?.name || "Freelancer"}!
          </p>
        </div>
      </AnimateOnScroll>

      {/* Stats Cards */}
      <AnimateOnScroll className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card
            className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Proposals
              </CardTitle>
              <Icon
                name="FileText"
                size="sm"
                className="text-muted-foreground"
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">+2 from last week</p>
            </CardContent>
          </Card>

          <Card
            className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
            style={{ animationDelay: "100ms" }}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                This Month Earnings
              </CardTitle>
              <Icon
                name="DollarSign"
                size="sm"
                className="text-muted-foreground"
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$1,250</div>
              <p className="text-xs text-muted-foreground">
                +15% from last month
              </p>
            </CardContent>
          </Card>

          <Card
            className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
            style={{ animationDelay: "200ms" }}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Success Rate
              </CardTitle>
              <Icon
                name="BarChart2"
                size="sm"
                className="text-muted-foreground"
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">68%</div>
              <p className="text-xs text-muted-foreground">
                +5% from last month
              </p>
            </CardContent>
          </Card>

          <Card
            className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
            style={{ animationDelay: "300ms" }}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Profile Views
              </CardTitle>
              <Icon name="Eye" size="sm" className="text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">234</div>
              <p className="text-xs text-muted-foreground">
                +12% from last week
              </p>
            </CardContent>
          </Card>
        </div>
      </AnimateOnScroll>

      {/* Recent Activity */}
      <AnimateOnScroll className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-2">
          <Card
            className={`${animations.cardHover} opacity-0 animate-slide-up`}
          >
            <CardHeader>
              <CardTitle>Recent Proposals</CardTitle>
              <CardDescription>Your latest job proposals</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                {
                  title: "E-commerce Website Development",
                  status: "pending",
                  time: "2 hours ago",
                },
                {
                  title: "Mobile App UI Design",
                  status: "accepted",
                  time: "1 day ago",
                },
                {
                  title: "Logo Design Project",
                  status: "rejected",
                  time: "3 days ago",
                },
              ].map((application, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{application.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {application.time}
                    </p>
                  </div>
                  <Badge
                    variant={
                      application.status === "accepted"
                        ? "success"
                        : application.status === "rejected"
                        ? "destructive"
                        : "secondary"
                    }
                  >
                    {application.status}
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card
            className={`${animations.cardHover} opacity-0 animate-slide-up`}
            style={{ animationDelay: "100ms" }}
          >
            <CardHeader>
              <CardTitle>Available Jobs</CardTitle>
              <CardDescription>
                New opportunities matching your skills
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                {
                  title: "React Developer Needed",
                  budget: "$500-1000",
                  posted: "1 hour ago",
                },
                {
                  title: "Content Writer for Blog",
                  budget: "$200-500",
                  posted: "3 hours ago",
                },
                {
                  title: "Social Media Manager",
                  budget: "$300-800",
                  posted: "5 hours ago",
                },
              ].map((job, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{job.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {job.posted}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{job.budget}</span>
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </AnimateOnScroll>
    </div>
  );
}
