/**
 * Utility to update user role in Cognito using Amplify Auth
 * This can be used by authenticated users to fix their own role or by admins
 */

import { updateUserAttributes } from 'aws-amplify/auth';
import { UserRole } from '@/types/enums';

export interface UpdateRoleResult {
  success: boolean;
  message: string;
  role?: UserRole;
}

/**
 * Update the current authenticated user's custom:role attribute
 */
export async function updateCurrentUserRole(role: UserRole): Promise<UpdateRoleResult> {
  try {
    if (!Object.values(UserRole).includes(role)) {
      return {
        success: false,
        message: `Invalid role: ${role}. Must be one of: ${Object.values(UserRole).join(', ')}`
      };
    }

    await updateUserAttributes({
      userAttributes: {
        'custom:role': role
      }
    });

    return {
      success: true,
      message: `Successfully updated role to ${role}`,
      role
    };

  } catch (error) {
    console.error('Error updating user role:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update role'
    };
  }
}

/**
 * Convenience function to set role to FREELANCER
 */
export async function setRoleToFreelancer(): Promise<UpdateRoleResult> {
  return updateCurrentUserRole(UserRole.FREELANCER);
}

/**
 * Convenience function to set role to CLIENT
 */
export async function setRoleToClient(): Promise<UpdateRoleResult> {
  return updateCurrentUserRole(UserRole.CLIENT);
}

/**
 * Convenience function to set role to ADMIN
 */
export async function setRoleToAdmin(): Promise<UpdateRoleResult> {
  return updateCurrentUserRole(UserRole.ADMIN);
}

if (typeof window !== 'undefined') {
  (window as any).updateUserRole = {
    updateCurrentUserRole,
    setRoleToFreelancer,
    setRoleToClient,
    setRoleToAdmin,
    UserRole
  };
}
