"use client";
import React, { useEffect, useState } from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/layouts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Icon,
} from "@/components/ui";
import { AnimateOnScroll } from "@/components/common/AnimateOnScroll";
import { animations } from "@/utils/animations";

export const dynamic = "force-dynamic";

export default function AdminDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!loading && isClient) {
      setIsAuthorized(true);
    }
  }, [loading, isClient]);

  if (!isClient || loading || !isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="opacity-0 animate-fade-in">
          <Icon
            name="Loader2"
            size="xl"
            className="animate-spin text-blue-500"
          />
        </div>
      </div>
    );
  }

  const userName = user?.attributes?.name || "Admin";

  return (
    <DashboardLayout
      breadcrumbs={[
        { label: "Admin", href: "/admin" },
        { label: "Dashboard", current: true },
      ]}
      title="Admin Dashboard"
      description={`Welcome back, ${userName}!`}
      actions={
        <Button onClick={() => router.push("/admin/settings")}>
          System Settings
        </Button>
      }
    >
      <div className="space-y-8">
        <AnimateOnScroll>
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">
              Admin Dashboard
            </h1>
            <p className="text-muted-foreground">Welcome back, {userName}!</p>
          </div>
        </AnimateOnScroll>

        {/* Stats Cards */}
        <AnimateOnScroll className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card
              className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Users
                </CardTitle>
                <Icon
                  name="Users"
                  size="sm"
                  className="text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <p className="text-xs text-muted-foreground">
                  +20% from last month
                </p>
              </CardContent>
            </Card>

            <Card
              className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
              style={{ animationDelay: "100ms" }}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Jobs
                </CardTitle>
                <Icon
                  name="Briefcase"
                  size="sm"
                  className="text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">89</div>
                <p className="text-xs text-muted-foreground">
                  +12% from last week
                </p>
              </CardContent>
            </Card>

            <Card
              className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
              style={{ animationDelay: "200ms" }}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Revenue
                </CardTitle>
                <Icon
                  name="DollarSign"
                  size="sm"
                  className="text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$45,231</div>
                <p className="text-xs text-muted-foreground">
                  +7% from last month
                </p>
              </CardContent>
            </Card>
          </div>
        </AnimateOnScroll>

        {/* Recent Activity and Quick Actions */}
        <AnimateOnScroll className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card
              className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
            >
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest system events and user actions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    action: "New user registered",
                    user: "<EMAIL>",
                    time: "2 minutes ago",
                  },
                  {
                    action: "Job posted",
                    user: "<EMAIL>",
                    time: "15 minutes ago",
                  },
                  {
                    action: "Application submitted",
                    user: "<EMAIL>",
                    time: "1 hour ago",
                  },
                  {
                    action: "Payment processed",
                    user: "<EMAIL>",
                    time: "2 hours ago",
                  },
                ].map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <p className="text-sm font-medium">{activity.action}</p>
                      <p className="text-xs text-muted-foreground">
                        {activity.user}
                      </p>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {activity.time}
                    </span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card
              className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
              style={{ animationDelay: "100ms" }}
            >
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common administrative tasks</CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push("/admin/users")}
                >
                  <Icon name="Users" size="lg" />
                  <span className="text-sm">Manage Users</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push("/admin/jobs")}
                >
                  <Icon name="Briefcase" size="lg" />
                  <span className="text-sm">Manage Jobs</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push("/admin/reports")}
                >
                  <Icon name="BarChart2" size="lg" />
                  <span className="text-sm">View Reports</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push("/admin/settings")}
                >
                  <Icon name="Settings" size="lg" />
                  <span className="text-sm">Settings</span>
                </Button>
              </CardContent>
            </Card>
          </div>
        </AnimateOnScroll>
      </div>
    </DashboardLayout>
  );
}
