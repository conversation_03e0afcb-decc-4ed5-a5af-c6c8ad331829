"use client";

import { useAuth } from "@/lib/auth/AuthContext";
import { useEffect, useState } from "react";
import { Loading, LoadingOverlay } from "@/components/ui";
import { MessagingProvider } from "@/contexts/MessagingContext";
import { MessagingUser } from "@/components/messaging/types";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { loading, user, isInitialized, isLoggingOut, cognitoUserId } =
    useAuth();

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || loading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const currentUser: MessagingUser = {
    id: cognitoUserId || user?.username || "",
    name: user?.attributes?.name || "User",
    email: user?.attributes?.email || "",
    avatar: user?.attributes?.profilePhoto || "",
    role: (user?.attributes?.["custom:role"] === 'FREELANCER' ? 'FREELANCER' : 'CLIENT') as 'CLIENT' | 'FREELANCER',
    isOnline: true,
  };

  const handleSendMessage = async (conversationId: string, content: string) => {
    console.log("Sending message:", { conversationId, content });
  };

  const handleLoadMoreMessages = async (
    conversationId: string,
    before: Date
  ) => {
    console.log("Loading more messages:", { conversationId, before });
    return [];
  };

  const handleFileUpload = async (file: File) => {
    console.log("Uploading file:", file.name);
    return URL.createObjectURL(file);
  };

  return (
    <MessagingProvider
      currentUser={currentUser}
      initialConversations={[]}
      onSendMessage={handleSendMessage}
      onLoadMoreMessages={handleLoadMoreMessages}
      onFileUpload={handleFileUpload}
    >
      {children}

      {/* Global Logout Loading Overlay */}
      {isLoggingOut && (
        <LoadingOverlay
          isLoading={isLoggingOut}
          className="fixed inset-0 z-[9999] bg-background/80 backdrop-blur-sm"
          loadingText="Signing out..."
        >
          {children}
        </LoadingOverlay>
      )}
    </MessagingProvider>
  );
}
