import Link from 'next/link';
import { But<PERSON> } from '@/components/ui';

export const dynamic = 'force-static';

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] text-center p-4">
      <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
      <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
        Page Not Found
      </h2>
      <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
        The page you&#39;re looking for doesn&#39;t exist or has been moved.
      </p>
      <Button asChild variant="default">
        <Link href="/">
          Go back home
        </Link>
      </Button>
    </div>
  );
}
