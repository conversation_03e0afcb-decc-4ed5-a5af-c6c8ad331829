import type { User as BaseUser } from "@/types/user";

/**
 * Message in a conversation
 */
export interface FileInfo {
  /** Name of the file */
  name: string;
  
  /** MIME type of the file */
  type?: string;
  
  /** Size of the file in bytes */
  size?: number;
  
  /** URL to access the file */
  url: string;
}

export interface Message {
  /** Unique message identifier */
  id: string;
  
  /** Message content */
  content: string;
  
  /** ID of the user who sent the message */
  senderId: string;
  
  /** ID of the message recipient */
  receiverId: string;
  
  /** ID of the conversation this message belongs to */
  conversationId: string;
  
  /** When the message was created (server timestamp) */
  createdAt: string;
  
  /** When the message was last updated */
  updatedAt: string | Date;
  
  /** User who sent the message */
  sender: {
    id: string;
    name: string;
    email: string;
    role: 'CLIENT' | 'FREELANCER';
    profilePhoto?: string;
    isOnline?: boolean;
  };
  
  /** User who received the message */
  receiver: {
    id: string;
    name: string;
    email: string;
    role: 'CLIENT' | 'FREELANCER';
    profilePhoto?: string;
    isOnline?: boolean;
  };
  
  /** Delivery status of the message */
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
  
  /** Type of message content */
  type?: 'text' | 'file' | 'image';
  
  /** File information (for file type messages) */
  fileInfo?: FileInfo;
  
  /** Whether the message is being sent */
  isSending?: boolean;
  
  /** Error message if sending failed */
  error?: string;
}

/**
 * Conversation between users
 */
export interface Conversation {
  /** Unique conversation identifier */
  id: string;
  
  /** Users participating in the conversation */
  participants: MessagingUser[];
  
  /** Last message in the conversation */
  lastMessage?: Omit<Message, 'id'> & { id?: string };
  
  /** Number of unread messages */
  unreadCount: number;
  
  /** When the conversation was last updated */
  updatedAt: Date;
}

/**
 * User in the context of messaging
 */
export interface MessagingUser extends Pick<BaseUser, 'id' | 'name' | 'email'> {
  /** URL to user's avatar */
  avatar?: string;
  
  /** User's role in the messaging context */
  role: "CLIENT" | "FREELANCER";
  
  /** Whether the user is currently online */
  isOnline: boolean;
  
  /** When the user was last seen */
  lastSeen?: Date;
}

export interface MessageInputProps {
  onSend: (content: string) => void;
  onTyping: (isTyping: boolean) => void;
  placeholder?: string;
  disabled?: boolean;
}

export interface MessageBubbleProps {
  /** The message to display */
  message: Message;
  
  /** Whether the message is from the current user */
  isCurrentUser: boolean;
  
  /** Whether to show the user's avatar */
  showAvatar: boolean;
  
  /** Whether to show the message timestamp */
  showTimestamp: boolean;
  
  /** The user who sent the message */
  user: MessagingUser;
}

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (id: string) => void;
  currentUserId: string;
  loading?: boolean;
}

export interface MessageThreadProps {
  /** List of messages in the thread */
  messages: Message[];
  
  /** The currently logged-in user */
  currentUser: MessagingUser;
  
  /** The other user in the conversation */
  otherUser: MessagingUser;
  
  /** Whether the thread is currently loading messages */
  loading?: boolean;
  
  /** Callback to load more messages */
  onLoadMore: () => void;
  
  /** Whether there are more messages to load */
  hasMore: boolean;
  
  /** Whether the other user is currently typing */
  isTyping: boolean;
}
