export interface FileInfo {
  url: string;
  name: string;
  size?: number;
  type?: string;
}

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'error';
export type MessageType = 'text' | 'file' | 'image';

interface BaseMessage {
  id: string;
  conversationId: string;
  senderId: string;
  createdAt: string;
  updatedAt: string;
  status: MessageStatus;
  type: MessageType;
  fileInfo?: FileInfo;
  isSending?: boolean;
  error?: string | null;
}

export interface UIMessage extends BaseMessage {
  content: string;
  receiverId: string;
  sender: MessagingUser;
  receiver: MessagingUser;
  messageText?: string;
}

export type ServerMessage = Omit<BaseMessage, 'content' | 'receiverId'> & {
  messageText: string;
  sender: MessagingUser;
};

export interface  MessagingUser {
  id: string;
  name: string;
  email: string;
  role: 'CLIENT' | 'FREELANCER';
  isOnline: boolean;
  avatar?: string;
  profilePhoto?: string;
}

export interface JobConversation {
  id: string;
  jobId: string;
  jobTitle: string;
  lastMessage?: {
    id: string;
    content?: string;
    senderId: string;
    conversationId?: string;
    createdAt: string;
    status?: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
    type?: 'text' | 'file' | 'image';
  };
  unreadCount?: number;
  isActive?: boolean;
}

export interface UserConversationGroup {
  userId: string;
  userName: string;
  userAvatar?: string;
  userRole: 'CLIENT' | 'FREELANCER';
  isOnline: boolean;
  jobs: JobConversation[];
  isExpanded: boolean;
}

export interface Conversation {
  id: string;
  jobId: string;
  clientId: string;
  freelancerId: string;
  createdAt: string;
  updatedAt: string;
  messages: UIMessage[];
  participants: MessagingUser[];
  client: MessagingUser;
  freelancer: MessagingUser;
  job: {
    id: string;
    title: string;
  };
  lastMessage?: {
    id: string;
    content?: string;
    senderId: string;
    conversationId?: string;
    createdAt: string;
    status?: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
    type?: 'text' | 'file' | 'image';
  };
  unreadCount?: number;
}
