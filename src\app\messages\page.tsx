'use client';

import { useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loading } from '@/components/ui';
import { Suspense } from 'react';

function MessagesRedirectContent() {
  const { user, isInitialized } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const jobId = searchParams.get('jobId');
  const to = searchParams.get('to');

  useEffect(() => {
    if (!isInitialized) return;

    const role = user?.attributes?.['custom:role']?.toLowerCase() || 'client';
    let redirectUrl = `/${role}/messages`;
    
    const queryParams = new URLSearchParams();
    if (jobId) queryParams.set('jobId', jobId);
    if (to) queryParams.set('to', to);
    
    if (queryParams.toString()) {
      redirectUrl += `?${queryParams.toString()}`;
    }

    router.replace(redirectUrl);
  }, [isInitialized, user, router, jobId, to]);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loading size="lg" />
      </div>
    );
  }

  return null;
}

export default function MessagesRedirectPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <Loading size="lg" />
      </div>
    }>
      <MessagesRedirectContent />
    </Suspense>
  );
}
