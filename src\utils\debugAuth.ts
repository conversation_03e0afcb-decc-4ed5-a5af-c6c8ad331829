/**
 * Debug utility to test authentication and role extraction
 */

import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';

export async function debugAuthState() {
  try {
    console.log('=== AUTH DEBUG START ===');
    
    const session = await fetchAuthSession();
    console.log('Session:', session);
    
    if (session.tokens?.accessToken) {
      const payload = session.tokens.accessToken.payload;
      console.log('Access Token Payload:', payload);
      
      const groups = payload['cognito:groups'] as string[] | undefined;
      console.log('Cognito Groups:', groups);
      
      if (groups && groups.length > 0) {
        for (const group of groups) {
          const groupUpper = group.toUpperCase();
          console.log(`Group: ${group} -> ${groupUpper}`);
          if (groupUpper === 'CLIENT' || groupUpper === 'FREELANCER' || groupUpper === 'ADMIN') {
            console.log(`✓ Found valid role in groups: ${groupUpper}`);
          }
        }
      }
    }
    
    if (session.tokens?.idToken) {
      const payload = session.tokens.idToken.payload;
      console.log('ID Token Payload:', payload);
      
      const customRole = payload['custom:role'];
      console.log('Custom Role from ID Token:', customRole);
    }
    
    const attributes = await fetchUserAttributes();
    console.log('User Attributes:', attributes);
    
    const customRole = attributes['custom:role'];
    console.log('Custom Role from Attributes:', customRole);
    
    console.log('=== AUTH DEBUG END ===');
    
    return {
      session,
      attributes,
      groups: session.tokens?.accessToken?.payload['cognito:groups'],
      customRole: attributes['custom:role']
    };
    
  } catch (error) {
    console.error('Debug auth error:', error);
    return null;
  }
}

if (typeof window !== 'undefined') {
  (window as any).debugAuth = debugAuthState;
}
