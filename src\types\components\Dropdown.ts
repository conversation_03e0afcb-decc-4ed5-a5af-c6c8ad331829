import { ReactNode } from 'react';

export interface DropdownItem {
  /** Unique identifier for the dropdown item */
  id: string;
  /** Content to display in the dropdown item */
  label: ReactNode;
  /** Optional icon to display before the label */
  icon?: ReactNode;
  /** Whether the item is disabled */
  disabled?: boolean;
  /** Callback when the item is clicked */
  onClick?: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show a divider after this item */
  withDivider?: boolean;
}

export interface DropdownProps {
  /** Trigger element that opens the dropdown */
  trigger: ReactNode;
  /** Array of dropdown items */
  items: DropdownItem[];
  /** Alignment of the dropdown relative to the trigger */
  align?: 'start' | 'center' | 'end' | 'left' | 'right';
  /** Side of the trigger to render the dropdown */
  side?: 'top' | 'right' | 'bottom' | 'left';
  /** Additional CSS classes */
  className?: string;
  /** Whether the dropdown is open by default */
  defaultOpen?: boolean;
  /** Callback when dropdown open state changes */
  onOpenChange?: (open: boolean) => void;
  /** Whether the dropdown should close when an item is clicked */
  closeOnSelect?: boolean;
}
