import { uploadData, getUrl, remove } from 'aws-amplify/storage';
import { Amplify } from 'aws-amplify';
import awsconfig from '@/aws-exports';

Amplify.configure(awsconfig);

export const S3_CONFIG = {
  bucket: awsconfig.aws_user_files_s3_bucket,
  region: awsconfig.aws_project_region,
  level: 'public',
  contentType: 'binary/octet-stream',
  acl: 'public-read',
  baseUrl: `https://${awsconfig.aws_user_files_s3_bucket}.s3.${awsconfig.aws_project_region}.amazonaws.com`
};


type StorageAccessLevel = 'guest' | 'protected' | 'private';

type UploadParams = {
  file: File;
  fileName: string;
  contentType?: string;
  level?: StorageAccessLevel;
  progressCallback?: (progress: { loaded: number; total: number }) => void;
};

type DeleteParams = {
  key: string;
  level?: StorageAccessLevel;
};

type GetUrlParams = {
  key: string;
  level?: StorageAccessLevel;
  expiresIn?: number;
};

const S3Service = {
  /**
   * Upload a file to S3
   * @param params Upload parameters
   * @returns The S3 key of the uploaded file
   */
  async uploadFile({
    file,
    fileName,
    contentType = file.type || 'binary/octet-stream',
    level = 'guest',
    progressCallback,
  }: UploadParams): Promise<string> {
    try {
      const cleanFileName = fileName
        .replace(/^\/+|\/+$/g, '')
        .replace(/^public\//g, '');

      const s3Key = cleanFileName;

      await uploadData({
        key: s3Key,
        data: file,
        options: {
          contentType,
          onProgress: ({ transferredBytes, totalBytes }) => {
            if (progressCallback) {
              progressCallback({
                loaded: transferredBytes,
                total: totalBytes || file.size,
              });
            }
          },
          accessLevel: level
        },
      }).result;

      return s3Key;
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw error;
    }
  },

  /**
   * Delete a file from S3
   * @param params Delete parameters
   */
  async deleteFile({
    key,
    level = 'guest',
  }: DeleteParams): Promise<void> {
    try {
      let cleanKey = key.replace(/^(public|protected|private)\//, '').replace(/^\/+|\/+$/g, '');

      console.log(`Deleting file with key: ${cleanKey} (level: ${level})`);

      if (level === 'protected') {
        cleanKey = cleanKey.replace(/^protected\//, '');

        if (cleanKey.startsWith('profile-photos/')) {
          cleanKey = cleanKey.replace(/^profile-photos\//, '');
          cleanKey = `profile-photos/${cleanKey}`;
        }
      }

      console.log(`Attempting to delete with final key: ${cleanKey}`);

      await remove({
        key: cleanKey,
        options: {
          accessLevel: level,
        },
      });

      console.log(`Successfully deleted file with key: ${cleanKey} (level: ${level})`);
    } catch (error) {
      console.error('Error deleting file from S3:', {
        key,
        level,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  },

  /**
   * Get a public URL for a file in S3
   * @param params Get URL parameters
   * @returns The public URL of the file
   */
  async getFileUrl({
    key,
    level = 'guest',
    expiresIn = 3600,
  }: GetUrlParams): Promise<string> {
    try {
      const cleanKey = this.extractKeyFromUrl(key) || key.replace(/^\/+|\/+$/g, '');

      if (level === 'guest') {
        return `${S3_CONFIG.baseUrl}/public/${cleanKey.replace(/^public\//, '')}`;
      }

      const { url } = await getUrl({
        key: cleanKey,
        options: {
          accessLevel: level,
          expiresIn
        }
      });

      return url.toString();
    } catch (error) {
      console.error('Error generating file URL:', error);
      throw error;
    }
  },

  /**
   * Extract the S3 key from a URL
   * @param url The S3 URL
   * @returns The S3 key
   */
  extractKeyFromUrl(url: string): string | null {
    if (!url) return null;

    const patterns = [
      /amazonaws.com\/([^?]+)/,
      /public\/([^?]+)/,
      /protected\/([^?]+)/,
      /private\/([^?]+)/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return decodeURIComponent(match[1]);
      }
    }

    return null;
  },
};

export default S3Service;
