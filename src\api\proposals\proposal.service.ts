import { gql } from '@apollo/client';
import { graphQLClient } from '../../lib/graphql/graphqlClient';
import type { JobProposal, CreateJobProposalInput, ProposalStatus } from '../../types/proposal';

const LIST_MY_PROPOSALS = gql`
  query ListMyProposals($freelancerId: ID!) {
    listProposals(filter: { freelancerId: { eq: $freelancerId } }) {
      items {
        id
        jobId
        freelancerId
        coverLetter
        bidAmount
        proposedRate
        status
        job {
          id
          title
          description
          budget
          category
          deadline
          status
          client {
            id
            name
            email
          }
        }
        createdAt
        updatedAt
      }
    }
  }
`;

const SUBMIT_PROPOSAL = gql`
  mutation CreateProposal($input: CreateJobProposalInput!) {
    createJobProposal(input: $input) {
      id
      jobId
      freelancerId
      coverLetter
      bidAmount
      proposedRate
      status
      createdAt
      updatedAt
    }
  }
`;

const WITHDRAW_PROPOSAL = gql`
  mutation WithdrawProposal($id: ID!) {
    deleteJobProposal(input: { id: $id }) {
      id
    }
  }
`;

const UPDATE_PROPOSAL_STATUS = gql`
  mutation UpdateProposalStatus($input: UpdateJobProposalInput!) {
    updateJobProposal(input: $input) {
      id
      status
      updatedAt
    }
  }
`;

const GET_JOB_PROPOSALS = gql`
  query GetJobProposals($jobId: ID!) {
    getJob(id: $jobId) {
      proposals {
        items {
          id
          jobId
          freelancerId
          coverLetter
          bidAmount
          proposedRate
          status
          freelancer {
            id
            name
            email
            profile {
              title
              skills
              experience
            }
          }
          createdAt
          updatedAt
        }
      }
    }
  }
`;

const HAS_SUBMITTED_PROPOSAL = gql`
  query HasSubmittedProposal($jobId: ID!, $freelancerId: ID!) {
    hasSubmittedProposal(jobId: $jobId, freelancerId: $freelancerId)
  }
`;

const GET_PROPOSAL = gql`
  query GetProposal($id: ID!) {
    getProposal(id: $id) {
      id
      jobId
      freelancerId
      coverLetter
      bidAmount
      proposedRate
      status
      createdAt
      updatedAt
      job {
        id
        title
        description
        budget
        category
        deadline
        status
        isRemote
        client {
          id
          name
          email
        }
      }
      freelancer {
        id
        name
        email
        profilePhoto
        bio
        skills
      }
    }
  }
`;

const handleApiError = <T>(operation: string, error: unknown): T => {
  console.error(`${operation} failed:`, error);
  throw error instanceof Error
    ? new Error(`Failed to ${operation}: ${error.message}`)
    : new Error(`Failed to ${operation}: ${String(error)}`);
};

export const proposalService = {
  /**
   * List all proposals for a specific freelancer
   * @param freelancerId - The ID of the freelancer
   * @returns Promise<JobProposal[]> - Array of proposals
   */
  async listMyProposals(freelancerId: string): Promise<JobProposal[]> {
    console.log('listMyProposals called with freelancerId:', freelancerId);
    try {
      console.log('Sending GraphQL query with variables:', { freelancerId });
      const response = await graphQLClient.execute<{ listProposals: { items: JobProposal[] } }>(
        LIST_MY_PROPOSALS,
        { freelancerId }
      );
      
      console.log('GraphQL response received:', JSON.stringify(response, null, 2));
      const items = response?.listProposals?.items || [];
      console.log('Extracted proposals:', items.length, 'items');
      return items;
    } catch (error) {
      return handleApiError('list proposals', error);
    }
  },

  /**
   * Submit a new job proposal
   * @param input - Proposal details
   * @returns Promise<JobProposal> - The created proposal
   */
  async submitProposal(input: CreateJobProposalInput): Promise<JobProposal> {
    try {
      const response = await graphQLClient.execute<{ createJobProposal: JobProposal }>(
        SUBMIT_PROPOSAL,
        { input }
      );
      return response.createJobProposal;
    } catch (error) {
      return handleApiError('submit proposal', error);
    }
  },

  /**
   * Withdraw a proposal
   * @param proposalId - The ID of the proposal to withdraw
   */
  async withdrawProposal(proposalId: string): Promise<void> {
    try {
      await graphQLClient.execute(
        WITHDRAW_PROPOSAL,
        { id: proposalId }
      );
    } catch (error) {
      handleApiError('withdraw proposal', error);
    }
  },

  /**
   * Update proposal status
   * @param id - Proposal ID
   * @param status - New status
   * @returns Promise<JobProposal> - Updated proposal
   */
  async updateProposalStatus(
    id: string,
    status: ProposalStatus
  ): Promise<JobProposal> {
    try {
      const response = await graphQLClient.execute<{ updateJobProposal: JobProposal }>(
        UPDATE_PROPOSAL_STATUS,
        {
          input: {
            id,
            status,
          },
        }
      );

      if (!response?.updateJobProposal) {
        throw new Error('Failed to update proposal status');
      }

      return response.updateJobProposal;
    } catch (error) {
      return handleApiError('update proposal status', error);
    }
  },

  /**
   * Update a proposal's details
   * @param id - The ID of the proposal to update
   * @param input - The updated proposal data
   * @returns Promise<JobProposal> - The updated proposal
   */
  async updateProposal(
    id: string,
    input: { coverLetter: string; bidAmount: number }
  ): Promise<JobProposal> {
    try {
      const response = await graphQLClient.execute<{ updateProposal: JobProposal }>(
        gql`
          mutation UpdateProposal($input: UpdateProposalInput!) {
            updateProposal(input: $input) {
              id
              jobId
              coverLetter
              bidAmount
              status
              updatedAt
            }
          }
        `,
        {
          input: {
            id,
            coverLetter: input.coverLetter,
            bidAmount: input.bidAmount
          }
        }
      );

      if (!response?.updateProposal) {
        throw new Error('Failed to update proposal');
      }

      return response.updateProposal;
    } catch (error) {
      return handleApiError('update proposal', error);
    }
  },

  /**
   * Get all proposals for a specific job
   * @param jobId - The ID of the job
   * @returns Promise<JobProposal[]> - Array of proposals for the job
   */
  async getJobProposals(jobId: string): Promise<JobProposal[]> {
    try {
      const response = await graphQLClient.execute<{ getJob: { proposals: { items: JobProposal[] } } }>(
        GET_JOB_PROPOSALS,
        { jobId }
      );
      return response.getJob.proposals.items;
    } catch (error) {
      return handleApiError('get job proposals', error);
    }
  },

  /**
   * Get a single proposal by ID
   * @param id - The ID of the proposal to fetch
   * @returns Promise<JobProposal | null> - The proposal if found, null otherwise
   */
  async getProposalById(id: string): Promise<JobProposal | null> {
    console.log('getProposalById called with id:', id);
    try {
      const response = await graphQLClient.execute<{ getProposal: JobProposal }>(
        GET_PROPOSAL,
        { id }
      );
      
      console.log('Proposal response:', response);
      return response?.getProposal || null;
    } catch (error) {
      console.error('Error fetching proposal:', error);
      return null;
    }
  },

  /**
   * Check if a freelancer has already submitted a proposal for a specific job
   * @param jobId - The ID of the job to check
   * @param freelancerId - The ID of the freelancer
   * @param job - Optional job object with proposals to check against
   * @returns boolean - True if the freelancer has already submitted a proposal for this job
   */
  async hasSubmittedProposal(
    jobId: string,
    freelancerId: string,
    job?: {
      proposals?: {
        items?: Array<{
          freelancerId: string;
          jobId?: string;
        }>;
      };
    }
  ): Promise<boolean> {
    try {
      if (job?.proposals?.items) {
        return job.proposals.items.some(
          (p) => p.freelancerId === freelancerId && (!p.jobId || p.jobId === jobId)
        );
      }

      const response = await graphQLClient.execute<{ hasSubmittedProposal: boolean }>(
        HAS_SUBMITTED_PROPOSAL,
        { jobId, freelancerId }
      );
      
      return response?.hasSubmittedProposal || false;
    } catch (error) {
      return handleApiError('has submitted proposal', error);
    }
  },
};

export default proposalService;
