'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';

export default function ConfirmDialogPreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const handleConfirm = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setIsOpen(false);
      setResult('Confirmed!');
    }, 1500);
  };

  const handleCancel = () => {
    setIsOpen(false);
    setResult('Cancelled!');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-3xl mx-auto space-y-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Confirm Dialog Preview</h1>
        
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Default Dialog</h2>
          <Button onClick={() => setIsOpen(true)}>Show Confirm Dialog</Button>
          
          {result && (
            <div className="mt-4 p-4 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-md">
              Action: {result}
            </div>
          )}
        </div>

        <div className="space-y-4 mt-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Variants</h2>
          <div className="flex flex-wrap gap-4">
            <Button 
              variant="destructive" 
              onClick={() => {
                setResult('');
                setIsOpen(true);
              }}
            >
              Delete Item
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => {
                setResult('');
                setIsOpen(true);
              }}
            >
              With Custom Message
            </Button>
          </div>
        </div>
      </div>

      <ConfirmDialog
        open={isOpen}
        title="Are you sure?"
        message="This action cannot be undone. This will permanently delete the item and all of its data."
        confirmText={isLoading ? 'Deleting...' : 'Delete'}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  );
}
