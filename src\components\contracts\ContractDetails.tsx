import React from 'react';
import { format } from 'date-fns';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { 
  Contract, 
  ContractStatus, 
  ContractType,
  PaymentSchedule,
  Deliverable 
} from '@/types/features/contracts/contract.types';
import { Separator } from '@/components/ui/Separator';

const statusColors: Record<ContractStatus, string> = {
  DRAFT: 'bg-gray-100 text-gray-800',
  PENDING_FREELANCER_ACCEPTANCE: 'bg-orange-100 text-orange-800',
  WORK_SUBMITTED: 'bg-purple-100 text-purple-800',
  REVISIONS_REQUESTED: 'bg-amber-100 text-amber-800',
  PAID: 'bg-emerald-100 text-emerald-800',
  ACTIVE: 'bg-blue-100 text-blue-800',
  COMPLETED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800',
  DISPUTED: 'bg-yellow-100 text-yellow-800',
};

const typeLabels: Record<ContractType, string> = {
  FIXED_PRICE: 'Fixed Price',
  HOURLY: 'Hourly Rate',
};

interface ContractDetailsProps {
  contract: Contract;
  paymentSchedule?: PaymentSchedule[];
  deliverables?: Deliverable[];
  isClient?: boolean;
  isFreelancer?: boolean;
  onAccept?: () => void;
  onReject?: () => void;
  onComplete?: () => void;
  onRequestChanges?: () => void;
  onDispute?: () => void;
  onDownload?: () => void;
  className?: string;
}

export const ContractDetails: React.FC<ContractDetailsProps> = ({
  contract,
  paymentSchedule = [],
  deliverables = [],
  isClient = false,
  isFreelancer = false,
  onAccept,
  onReject,
  onComplete,
  onRequestChanges,
  onDispute,
  onDownload,
  className = '',
}) => {
  const {
    title,
    description,
    status,
    type,
    budget,
    terms,
    startDate,
    endDate,
  } = contract;

  const formattedStartDate = format(new Date(startDate), 'MMMM d, yyyy');
  const formattedEndDate = endDate ? format(new Date(endDate), 'MMMM d, yyyy') : 'Ongoing';

  const renderActionButtons = () => {
    if (status === 'COMPLETED' || status === 'CANCELLED' || status === 'DISPUTED') {
      return (
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onDownload}>
            Download Contract
          </Button>
        </div>
      );
    }

    return (
      <div className="flex justify-end space-x-2">
        {status === 'PENDING_FREELANCER_ACCEPTANCE' && isFreelancer && (
          <>
            <Button variant="outline" onClick={onReject}>
              Reject
            </Button>
            <Button onClick={onAccept}>Accept Contract</Button>
          </>
        )}
        {status === 'WORK_SUBMITTED' && isClient && (
          <>
            <Button onClick={onComplete}>Approve Work</Button>
            <Button variant="outline" onClick={onRequestChanges}>
              Request Changes
            </Button>
          </>
        )}
        {(status === 'ACTIVE' || status === 'REVISIONS_REQUESTED') && isFreelancer && (
          <Button onClick={onComplete}>Submit Work</Button>
        )}
        <Button variant="outline" onClick={onDownload}>
          Download
        </Button>
        <Button variant="destructive" onClick={onDispute}>
          Raise Dispute
        </Button>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl font-bold">{title}</CardTitle>
              <div className="flex items-center mt-2 space-x-2">
                <Badge className={statusColors[status]}>{status.replace('_', ' ')}</Badge>
                <span className="text-sm text-muted-foreground">
                  {typeLabels[type]} • ${budget.toLocaleString()}
                </span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">Start Date</div>
              <div className="font-medium">{formattedStartDate}</div>
              {endDate && (
                <>
                  <div className="text-sm text-muted-foreground mt-1">End Date</div>
                  <div className="font-medium">{formattedEndDate}</div>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Description</h3>
              <p className="text-muted-foreground whitespace-pre-line">{description}</p>
            </div>

            <Separator />

            <div>
              <h3 className="font-medium mb-2">Terms & Conditions</h3>
              <div className="bg-muted p-4 rounded-md">
                <p className="whitespace-pre-line">{terms}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {paymentSchedule.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Payment Schedule</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {paymentSchedule.map((payment) => (
                <div key={payment.id} className="flex justify-between items-center p-3 border rounded-md">
                  <div>
                    <div className="font-medium">${payment.amount.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">
                      Due: {format(new Date(payment.dueDate), 'MMM d, yyyy')}
                    </div>
                    {payment.description && (
                      <p className="text-sm mt-1">{payment.description}</p>
                    )}
                  </div>
                  <Badge variant={payment.status === 'PAID' ? 'default' : 'outline'}>
                    {payment.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {deliverables.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Deliverables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {deliverables.map((deliverable) => (
                <div key={deliverable.id} className="border rounded-md p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{deliverable.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        Due: {deliverable.dueDate ? format(new Date(deliverable.dueDate), 'MMM d, yyyy') : 'N/A'}
                      </p>
                      {deliverable.description && (
                        <p className="mt-1 text-sm">{deliverable.description}</p>
                      )}
                      {deliverable.attachments && deliverable.attachments.length > 0 && (
                        <div className="mt-2">
                          <span className="text-sm font-medium">Attachments:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {deliverable.attachments.map((attachment, index) => (
                              <a
                                key={index}
                                href={attachment}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:underline"
                              >
                                File {index + 1}
                              </a>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <Badge variant="outline">{deliverable.status}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end">{renderActionButtons()}</div>
    </div>
  );
};

export default ContractDetails;
