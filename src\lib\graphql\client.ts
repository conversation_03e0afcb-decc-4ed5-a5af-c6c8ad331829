import { generateClient } from 'aws-amplify/api';
import { fetchAuthSession } from 'aws-amplify/auth';
import type { DocumentNode } from '@apollo/client';
import { print } from 'graphql';
import { APPSYNC_CONFIG } from '@/config/appsync';

type GraphQLAuthMode = 'apiKey' | 'iam' | 'oidc' | 'userPool' | 'lambda' | 'none';

type GraphQLResponse<T> = {
  data?: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: string[];
    extensions?: Record<string, unknown>;
  }>;
  extensions?: Record<string, unknown>;
};

const client = generateClient();

// Type guard to check if the result is a GraphQL response
// This is currently unused but kept for future reference
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function isGraphQLResponse<T>(result: unknown): result is GraphQLResponse<T> {
  return (
    typeof result === 'object' &&
    result !== null &&
    ('data' in result || 'errors' in result)
  );
}

// Type guard to check if the result is a GraphQL error response
// This is currently unused but kept for future reference
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function isGraphQLErrorResponse<T>(
  result: GraphQLResponse<T>
): result is { errors: NonNullable<GraphQLResponse<T>['errors']> } {
  return 'errors' in result && Array.isArray(result.errors) && result.errors.length > 0;
}

type GraphQLVariables = Record<string, unknown>;

export async function graphqlRequest<T = unknown, V = Record<string, unknown>>(
  query: string | DocumentNode,
  variables?: V,
  options: { authMode?: GraphQLAuthMode } = {}
): Promise<T> {
  try {
    const authToken =
      options.authMode === 'userPool'
        ? (await fetchAuthSession()).tokens?.idToken?.toString()
        : undefined;


    const queryString = typeof query === 'string' ? query : print(query);

    const operation: {
      query: string;
      variables?: GraphQLVariables;
      authMode?: typeof options.authMode;
      authToken?: string;
    } = {
      query: queryString,
      ...(variables && { variables }),
      ...(options.authMode && { authMode: options.authMode }),
      ...(authToken && { authToken }),
    };

    const result = await client.graphql({
      query: operation.query,
      variables: operation.variables as Record<string, unknown>,
      authMode: options.authMode as GraphQLAuthMode | undefined,
      ...(operation.authToken && { authToken: operation.authToken }),
    });

    if ('errors' in result && result.errors && result.errors.length > 0) {
      const errorMessages = result.errors.map(e => e.message).join('\n');
      throw new Error(`GraphQL error: ${errorMessages}`);
    }

    if ('data' in result === false) {
      throw new Error('Unexpected response format from GraphQL endpoint');
    }

    const typedResult = result as { data: T };
    
    if (!typedResult.data) {
      throw new Error('No data returned from GraphQL query');
    }

    return typedResult.data;
  } catch (error) {
    console.error('GraphQL request failed:', error);
    throw error;
  }
}

export async function graphqlFetch<T = unknown, V = Record<string, unknown>>(
  query: string | DocumentNode,
  variables?: V,
  headers: Record<string, string> = {}
): Promise<T> {
  try {
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (APPSYNC_CONFIG.AUTH_TYPE === 'API_KEY' && APPSYNC_CONFIG.API_KEY) {
      defaultHeaders['x-api-key'] = APPSYNC_CONFIG.API_KEY;
    }

    const response = await fetch(APPSYNC_CONFIG.GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        ...defaultHeaders,
        ...headers,
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      const error = new Error(`Request failed with status ${response.status}: ${response.statusText}`);
      console.error('GraphQL request failed:', {
        status: response.status,
        statusText: response.statusText,
        errors: responseData.errors,
        error
      });
      throw error;
    }

    if (responseData.errors) {
      console.error('GraphQL errors:', responseData.errors);
      throw new Error(
        responseData.errors[0]?.message ||
        'GraphQL request failed'
      );
    }

    return responseData.data;
  } catch (error) {
    console.error('GraphQL request error:', error);
    if (error instanceof Error) {
      if (error.message.includes('401')) {
        localStorage.removeItem('token');
        window.location.href = '/auth/login';
      }
      throw error;
    }
    throw new Error('Failed to execute GraphQL request');
  }
}
