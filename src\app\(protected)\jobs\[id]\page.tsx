"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import type { JobWithProposalList, JobStatus } from "@/types/job";
import type { CreateJobProposalInput } from "@/types/proposal";
import { ProposalStatus, type JobProposal } from "@/types/proposal.types";
import { jobService } from '@/api/jobs/job.service';
import { useMessaging } from "@/hooks/useMessaging";
import { formatDistanceToNow } from "date-fns";
import { toast } from "react-hot-toast";
import { Icon } from "@/components/ui/Icon";
import { Button } from "@/components/ui/Button";
import { DashboardLayout } from "@/components/layouts/DashboardLayout";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { ProposalSubmissionForm } from "@/components/proposals/ProposalSubmissionForm";
import { useContractCheck, shouldShowCreateContractButton, getCreateContractUrl } from '@/hooks/useContractCheck';

// Component to display accepted proposals with contract creation option
const AcceptedProposalsSection: React.FC<{
  job: JobWithProposalList | null;
}> = ({ job }) => {
  if (!job?.proposals) return null;

  const acceptedProposals = job.proposals.filter(
    (proposal: JobProposal) => proposal.status === ProposalStatus.ACCEPTED
  );

  if (acceptedProposals.length === 0) return null;

  return (
    <div className="mt-8 pt-6 border-t border-border">
      <h3 className="text-lg font-medium text-foreground mb-4">Accepted Proposals</h3>
      <div className="space-y-4">
        {acceptedProposals.map((proposal: JobProposal) => (
          <AcceptedProposalCard
            key={proposal.id}
            proposal={proposal}
            job={job}
          />
        ))}
      </div>
    </div>
  );
};

// Component for individual accepted proposal card
const AcceptedProposalCard: React.FC<{
  proposal: JobProposal;
  job: JobWithProposalList;
}> = ({ proposal, job }) => {
  const { hasExistingContract, isLoading } = useContractCheck(proposal.id);

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Icon name="CheckCircle" className="h-5 w-5 text-green-600 mr-3" />
          <div>
            <h4 className="text-lg font-medium text-green-900">
              Proposal Accepted
            </h4>
            <p className="text-green-700 mt-1">
              {proposal.freelancer?.name || 'Freelancer'} • ${proposal.bidAmount}
            </p>
          </div>
        </div>

        {shouldShowCreateContractButton(proposal.status, hasExistingContract) && (
          <div className="flex items-center gap-2">
            <span className="text-green-700">→</span>
            {isLoading ? (
              <Button size="sm" disabled>
                <Icon name="Loader2" size="sm" className="mr-2 animate-spin" />
                Checking...
              </Button>
            ) : (
              <Button size="sm" asChild>
                <Link href={getCreateContractUrl(job.id, proposal.id)}>
                  <Icon name="FileText" size="sm" className="mr-2" />
                  Create Contract
                </Link>
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const JobDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const {
    isAuthenticated,
    user,
    cognitoUserId,
    loading: authLoading,
  } = useAuth();
  const router = useRouter();
  const [job, setJob] = useState<JobWithProposalList | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmittingProposal, setIsSubmittingProposal] = useState(false);
  const [hasSubmittedProposal, setHasSubmittedProposal] = useState(false);
  const [showProposalForm, setShowProposalForm] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const userRole = user?.attributes?.["custom:role"];
  const isFreelancer = userRole === "FREELANCER";
  const isClient = userRole === "CLIENT";

  useEffect(() => {
    console.log("=== Debug: User Info ===");
    console.log("User role:", userRole);
    console.log("isClient:", isClient);
    console.log("User username:", user?.username);

    if (job) {
      console.log("=== Debug: Job Info ===");
      console.log("Job clientId:", job.clientId);
      console.log("Job clientId:", job.clientId);
      console.log("User sub (Cognito ID):", user?.attributes?.sub);
      console.log(
        "Show View Proposals button:",
        isClient && job.clientId === user?.attributes?.sub
      );

      console.log("Full job object:", JSON.parse(JSON.stringify(job)));
    }
  }, [job, user, isClient, userRole]);

  useEffect(() => {}, [
    userRole,
    isFreelancer,
    isClient,
    job,
    hasSubmittedProposal,
    showProposalForm,
    isAuthenticated,
  ]);

  const fetchJobAndProposalStatus = useCallback(async (): Promise<void> => {
    if (!id) return;

    try {
      setIsLoading(true);
      setError(null);

      const jobData = await jobService.getJob(id);

      const { proposals = [] } = jobData;

      const userProposal = proposals.find(
        (p: any) => p.freelancerId === cognitoUserId
      );
      const hasProposal = !!userProposal;

      let jobStatus = jobData.status || "OPEN";
      const hasAcceptedProposal = proposals.some(
        (p: any) => p.status === ProposalStatus.ACCEPTED
      );

      if (hasAcceptedProposal && jobStatus === "OPEN") {
        try {
          await jobService.updateJob({
            id: jobData.id,
            status: "IN_PROGRESS",
          });
          jobStatus = "IN_PROGRESS";

          jobData.status = "IN_PROGRESS";
        } catch (updateError) {
          console.error("Error updating job status:", updateError);
        }
      }

      setJob(jobData);

      if (isFreelancer) {
        setHasSubmittedProposal(hasProposal);
      } else {
        setHasSubmittedProposal(false);
      }
    } catch (err) {
      console.error("Error fetching job:", err);
      setError("Failed to load job details. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [id, isFreelancer, cognitoUserId]);

  useEffect(() => {
    fetchJobAndProposalStatus();
  }, [fetchJobAndProposalStatus]);

  const { startThreadFromApplication } = useMessaging();

  const handleProposalSubmit = async (proposalData: CreateJobProposalInput) => {
    try {
      if (!cognitoUserId || !job) {
        throw new Error("User not authenticated or job not found");
      }

      setIsSubmittingProposal(true);

      const submissionData = {
        ...proposalData,
        coverLetter: proposalData.coverLetter || "",
      };

      await jobService.submitProposal(submissionData, cognitoUserId);

      await startThreadFromApplication(job.id, job.clientId, cognitoUserId);

      const currentProposals = job?.proposals || [];

      const updatedProposals = [
        ...currentProposals,
        {
          ...submissionData,
          id: `temp-${Date.now()}`,
          jobId: submissionData.jobId,
          freelancerId: cognitoUserId,
          coverLetter: submissionData.coverLetter || "",
          bidAmount: submissionData.bidAmount,
          status: ProposalStatus.PENDING,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          freelancer: {
            id: cognitoUserId,
            name: "",
            email: "",
          },
        },
      ] as JobProposal[];

      setJob((prev) => {
        if (!prev) return null;

        const newProposalCount = updatedProposals.length;
        const hasAcceptedProposal = updatedProposals.some(
          (p) => p.status === ProposalStatus.ACCEPTED
        );
        const newStatus = hasAcceptedProposal
          ? ("IN_PROGRESS" as JobStatus)
          : prev.status;

        if (hasAcceptedProposal && prev.status === "OPEN") {
          jobService
            .updateJob({
              id: prev.id,
              status: "IN_PROGRESS" as JobStatus,
            })
            .catch(console.error);
        }

        return {
          ...prev,
          status: newStatus as JobStatus,
          proposals: updatedProposals,
          proposalCount: newProposalCount,
          updatedAt: new Date().toISOString(),
        } as JobWithProposalList;
      });

      setHasSubmittedProposal(true);
      setShowProposalForm(false);

      toast.success("Proposal submitted successfully!");

      setTimeout(() => {
        router.push("/freelancer/proposals");
      }, 2000);
    } catch (err) {
      console.error("Error submitting proposal:", err);

      if (err instanceof Error) {
        if (
          err.message.includes("duplicate") ||
          err.message.includes("already submitted")
        ) {
          toast.error("You have already submitted a proposal for this job.");
          setHasSubmittedProposal(true);
        } else if (
          err.message.includes("authentication") ||
          err.message.includes("unauthorized")
        ) {
          toast.error("Authentication error. Please log in again.");
          router.push("/login");
        } else {
          toast.error("Failed to submit proposal. Please try again.");
        }
      } else {
        toast.error("Failed to submit proposal. Please try again.");
      }
    } finally {
      setIsSubmittingProposal(false);
    }
  };

  const handleShowProposalForm = () => {
    setShowProposalForm(true);
  };

  const handleCancelProposal = () => {
    setShowProposalForm(false);
  };

  if (authLoading || !isAuthenticated || isLoading || !job) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          {error && <div className="text-red-500 mb-4">{error}</div>}
          {isLoading ? (
            <div>Loading job details...</div>
          ) : !isAuthenticated ? (
            <div>Please log in to view this job</div>
          ) : (
            <div>Loading job details...</div>
          )}
          <Icon
            name="Loader2"
            size="xl"
            className="animate-spin text-primary"
          />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-red-100">
              <Icon name="XCircle" size="lg" className="text-red-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">Error</h3>
            <p className="mt-2 text-sm text-muted-foreground">{error}</p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push("/")}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!job) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-blue-100">
              <Icon name="Info" size="lg" className="text-blue-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">
              Job Not Found
            </h3>
            <p className="mt-2 text-sm text-muted-foreground">
              The job you&#39;re looking for doesn&#39;t exist or has been
              removed.
            </p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push("/")}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const formattedDate = job.createdAt
    ? formatDistanceToNow(new Date(job.createdAt), { addSuffix: true })
    : "Date not available";

  const getStatusInfo = (status: string) => {
    const hasAcceptedProposal = job?.proposals?.some(
      (p) => p.status === ProposalStatus.ACCEPTED
    );
    const displayStatus = hasAcceptedProposal ? "IN_PROGRESS" : status;

    switch (displayStatus) {
      case "IN_PROGRESS":
        return {
          text: "In Progress",
          color: "bg-yellow-100 text-yellow-800",
          icon: "Clock",
        };
      case "COMPLETED":
        return {
          text: "Completed",
          color: "bg-green-100 text-green-800",
          icon: "CheckCircle",
        };
      case "CANCELLED":
        return {
          text: "Cancelled",
          color: "bg-red-100 text-red-800",
          icon: "XCircle",
        };
      case "OPEN":
      default:
        return {
          text: "Open",
          color: "bg-blue-100 text-blue-800",
          icon: "CircleDollarSign",
        };
    }
  };

  const statusInfo = getStatusInfo(job.status);

  const deadlineDate = job.deadline
    ? new Date(job.deadline).toLocaleDateString()
    : "No deadline specified";

  return (
    <DashboardLayout>
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <ContentHeader
          title={job.title}
          subtitle={job.category?.replace("_", " ")}
          showBackButton={true}
          backButtonLabel="Back"
          breadcrumbs={[
            {
              label: "Home",
              href: isFreelancer
                ? "/freelancer/dashboard"
                : "/client/dashboard",
            },
            {
              label: "Jobs",
              href: isFreelancer ? "/freelancer/jobs" : "/client/jobs",
            },
            { label: job.title, current: true },
          ]}
        />

        <div className="w-full mx-auto py-4">
          <div className="bg-card rounded-lg shadow-sm border p-8">
            <div className="space-y-6">
              <div className="flex flex-wrap items-center justify-between gap-4 text-sm">
                <div className="flex flex-wrap items-center gap-4">
                  <div className="flex items-center text-muted-foreground">
                    <Icon
                      name="Briefcase"
                      size="sm"
                      className="mr-1.5 text-muted-foreground/70"
                    />
                    {job.category?.replace("_", " ")}
                  </div>
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  <span
                    className={`inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${statusInfo.color}`}
                  >
                    <Icon
                      name={statusInfo.icon as any}
                      size="xs"
                      className="mr-1.5"
                    />
                    {statusInfo.text}
                    {job.proposalCount
                      ? ` • ${job.proposalCount} ${
                          job.proposalCount === 1 ? "Proposal" : "Proposals"
                        }`
                      : ""}
                  </span>
                </div>
                {job.budget && (
                  <div className="flex items-center text-muted-foreground">
                    <Icon
                      name="DollarSign"
                      size="sm"
                      className="mr-1.5 text-muted-foreground/70"
                    />
                    {job.budget.toLocaleString()}
                  </div>
                )}
                <div className="flex items-center text-muted-foreground">
                  <Icon
                    name="Calendar"
                    size="sm"
                    className="mr-1.5 text-muted-foreground/70"
                  />
                  {deadlineDate}
                </div>
                {job.isRemote && (
                  <div className="flex items-center text-sm text-green-600">
                    <Icon
                      name="MapPin"
                      size="sm"
                      className="mr-1.5 text-green-500"
                    />
                    Remote
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  DESCRIPTION
                </h3>
                <div className="prose max-w-none text-foreground">
                  {job.description.split("\n").map((paragraph, i) => (
                    <p key={i} className="mb-4 last:mb-0">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              <div className="space-y-6">
                {job.skills && job.skills.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-muted-foreground">
                      REQUIRED SKILLS
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {job.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {job.location && !job.isRemote && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-muted-foreground">
                      LOCATION
                    </h3>
                    <div className="flex items-center text-foreground">
                      <Icon
                        name="MapPin"
                        size="sm"
                        className="mr-2 text-muted-foreground/70"
                      />
                      {job.location}
                    </div>
                  </div>
                )}

                <div className="pt-6 border-t border-border">
                  <h3 className="text-sm font-medium text-muted-foreground mb-3">
                    CLIENT INFORMATION
                  </h3>
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                        <Icon
                          name="User"
                          size="sm"
                          className="text-muted-foreground"
                        />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-foreground">
                        {job.client?.name ||
                          job.client?.email ||
                          "Unknown Client"}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Posted {formattedDate}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {isFreelancer && job?.status === "OPEN" && (
                <div className="mt-8 pt-6 border-t border-border">
                  {hasSubmittedProposal ? (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <div className="flex items-center">
                        <Icon
                          name="CheckCircle"
                          className="h-5 w-5 text-blue-600 mr-3"
                        />
                        <div>
                          <h3 className="text-lg font-medium text-blue-900">
                            Proposal Submitted
                          </h3>
                          <p className="text-blue-700 mt-1">
                            You have already submitted a proposal for this job.
                            You can view and manage your proposals in your
                            dashboard.
                          </p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <Button asChild variant="outline" size="sm">
                          <Link href="/freelancer/proposals">
                            <Icon name="Briefcase" className="mr-2 h-4 w-4" />
                            View My Proposals
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ) : showProposalForm ? (
                    <ProposalSubmissionForm
                      jobId={id as string}
                      jobBudget={job.budget}
                      onSubmit={handleProposalSubmit}
                      onCancel={handleCancelProposal}
                      isSubmitting={isSubmittingProposal}
                    />
                  ) : (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium text-green-900">
                            Ready to Apply?
                          </h3>
                          <p className="text-green-700 mt-1">
                            Submit your proposal to show the client why
                            you&#39;re the perfect fit for this job.
                          </p>
                        </div>
                        <Button
                          size="sm"
                          onClick={handleShowProposalForm}
                          className="ml-4"
                        >
                          <Icon name="Send" className="mr-2 h-4 w-4" />
                          Submit Proposal
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Accepted Proposals Section */}
              <AcceptedProposalsSection job={job} />

              {isClient && job?.clientId === user?.attributes?.sub && (
                <div className="mt-8 pt-6 border-t border-border flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => router.push(`/client/jobs/${id}/edit`)}
                    className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 cursor-pointer"
                  >
                    <Icon name="Edit" size="sm" className="mr-2" />
                    Edit Job
                  </button>
                  <button
                    type="button"
                    onClick={() => router.push(`/client/jobs/${id}/proposals`)}
                    className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 cursor-pointer"
                  >
                    <Icon name="Users" size="sm" className="mr-2" />
                    View Proposals
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default JobDetailsPage;
