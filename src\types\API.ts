import { GraphQLResult } from '@aws-amplify/api';

export interface OnCreateMessageSubscription {
  onCreateMessage?: {
    __typename: 'Message';
    id: string;
    messageText: string;
    conversationId: string;
    senderId: string;
    receiverId: string;
    createdAt: string;
    updatedAt: string;
    sender?: {
      __typename: 'User';
      id: string;
      name: string;
      email: string;
      profilePhoto?: string | null;
    } | null;
    receiver?: {
      __typename: 'User';
      id: string;
      name: string;
      email: string;
      profilePhoto?: string | null;
    } | null;
  } | null;
}

export type SubscribeToMessagesSubscription = {
  subscribeToMessages: OnCreateMessageSubscription;
};

export interface CreateMessageInput {
  id?: string | null;
  messageText: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface CreateConversationInput {
  id?: string | null;
  jobId: string;
  clientId: string;
  freelancerId: string;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface Message {
  __typename: 'Message';
  id: string;
  messageText: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  createdAt: string;
  updatedAt: string;
  sender: User;
  receiver: User;
}

export interface User {
  __typename: 'User';
  id: string;
  name: string;
  email: string;
  profilePhoto?: string | null;
  role: 'CLIENT' | 'FREELANCER';
}

export interface Conversation {
  __typename: 'Conversation';
  id: string;
  jobId: string;
  clientId: string;
  freelancerId: string;
  createdAt: string;
  updatedAt: string;
  messages: {
    __typename: 'ModelMessageConnection';
    items: Array<Message>;
    nextToken?: string | null;
  };
  client: User;
  freelancer: User;
  job: {
    __typename: 'Job';
    id: string;
    title: string;
  };
}

export type GraphQLResponse<T> = Promise<GraphQLResult<T>>;
