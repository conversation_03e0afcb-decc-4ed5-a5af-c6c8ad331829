import { graphQLClient } from '../../lib/graphql/graphqlClient';
import { GET_JOB, LIST_JOBS } from './job.queries';
import {
  CREATE_JOB,
  UPDATE_JOB,
  DELETE_JOB,
  SUBMIT_PROPOSAL,
  UPDATE_PROPOSAL_STATUS,
  WITHDRAW_PROPOSAL
} from './job.mutations';
import type { Job, CreateJobInput, UpdateJobInput, JobFilter, JobWithProposalList } from '../../types/job';
import type { JobProposal, CreateJobProposalInput, ProposalStatus } from '../../types/proposal.types';

export const jobApi = {
  createJob: async (input: CreateJobInput) => {
    const response = await graphQLClient.mutate<{ createJob: Job }>(
      CREATE_JOB,
      { input }
    );
    return response.createJob;
  },

  getJob: async (id: string) => {
    const response = await graphQLClient.execute<{ getJob: JobWithProposalList }>(
      GET_JOB,
      { id },
      { authMode: 'userPool' }
    );
    return response.getJob;
  },

  updateJob: async (input: UpdateJobInput) => {
    const response = await graphQLClient.mutate<{ updateJob: Job }>(
      UPDATE_JOB,
      { input }
    );
    return response.updateJob;
  },

  deleteJob: async (id: string) => {
    await graphQLClient.mutate<{ deleteJob: { id: string } }>(
      DELETE_JOB,
      { input: { id } },
      { authMode: 'userPool' }
    );
  },

  listJobs: async (filter?: JobFilter) => {
    const response = await graphQLClient.query<{ listJobs: { items: Job[]; nextToken?: string } }>(
      LIST_JOBS,
      {
        filter: filter ? jobApi.transformFilter(filter) : undefined,
        limit: filter?.limit,
        nextToken: filter?.nextToken
      }
    );
    return response?.listJobs || { items: [], nextToken: undefined };
  },

  submitProposal: async (input: CreateJobProposalInput) => {
    const response = await graphQLClient.mutate<{ createProposal: JobProposal }>(
      SUBMIT_PROPOSAL,
      { input }
    );
    return response.createProposal;
  },

  updateProposalStatus: async (input: { id: string; status: ProposalStatus }) => {
    const response = await graphQLClient.mutate<{ updateProposal: JobProposal }>(
      UPDATE_PROPOSAL_STATUS,
      { input }
    );
    return response.updateProposal;
  },

  withdrawProposal: async (proposalId: string) => {
    await graphQLClient.mutate<{ deleteProposal: { id: string } }>(
      WITHDRAW_PROPOSAL,
      { input: { id: proposalId } },
      { authMode: 'userPool' }
    );
  },

  transformFilter(filter: JobFilter): any {
    const transformed: any = {};
    
    if (filter.clientId) {
      transformed.clientId = { eq: filter.clientId };
    }
    
    if (filter.category) {
      transformed.category = { eq: filter.category };
    }
    
    if (filter.minBudget !== undefined || filter.maxBudget !== undefined) {
      const budget: any = {};
      if (filter.minBudget !== undefined) budget.ge = filter.minBudget;
      if (filter.maxBudget !== undefined) budget.le = filter.maxBudget;
      transformed.budget = budget;
    }
    
    if (filter.isRemote !== undefined) {
      transformed.isRemote = { eq: filter.isRemote };
    }
    
    if (filter.status) {
      transformed.status = { eq: filter.status };
    }
    
    if (filter.searchTerm) {
      transformed.or = [
        { title: { contains: filter.searchTerm } },
        { description: { contains: filter.searchTerm } }
      ];
    }
    
    return transformed;
  }
};
