import type { Job, CreateJobInput, UpdateJob<PERSON>n<PERSON>, <PERSON><PERSON><PERSON><PERSON>, JobWithProposalList } from '../../types/job';
import type { JobProposal, CreateJobProposalInput, ProposalStatus } from '../../types/proposal.types';
import { jobApi } from './job.api';

interface ProposalsConnection {
  items?: JobProposal[];
  nodes?: JobProposal[];
  nextToken?: string;
}

function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const jobService = {
  async createJob(input: CreateJobInput): Promise<Job> {
    try {
      const job = await jobApi.createJob(input);
      return job;
    } catch (error) {
      return handleApiError('createJob', error);
    }
  },

  async getJob(id: string): Promise<JobWithProposalList> {
    try {
      const job = await jobApi.getJob(id);

      if (!job) {
        throw new Error('Job not found');
      }

      let proposals: JobProposal[] = [];
      const jobProposals = job.proposals as ProposalsConnection | JobProposal[] | undefined;

      if (jobProposals) {
        if (Array.isArray(jobProposals)) {
          proposals = jobProposals;
        } else if (Array.isArray(jobProposals.items)) {
          proposals = jobProposals.items;
        } else if (Array.isArray(jobProposals.nodes)) {
          proposals = jobProposals.nodes;
        }
      }

      const client = job.client ? {
        id: job.client.id,
        name: job.client.name || 'Unknown Client',
        email: job.client.email || ''
      } : undefined;

      return {
        ...job,
        status: job.status || 'OPEN',
        proposals,
        proposalCount: proposals.length,
        client
      };
    } catch (error) {
      console.error('Error in getJob:', error);
      throw error;
    }
  },

  async updateJob(input: UpdateJobInput): Promise<Job> {
    try {
      const job = await jobApi.updateJob(input);
      if (!job) {
        throw new Error('Failed to update job: No data returned');
      }
      return job;
    } catch (error) {
      return handleApiError('updateJob', error);
    }
  },

  async deleteJob(id: string): Promise<void> {
    try {
      await jobApi.deleteJob(id);
    } catch (error) {
      handleApiError('deleteJob', error);
    }
  },

  async listJobs(filter?: JobFilter): Promise<{ items: Job[]; nextToken?: string }> {
    try {
      const result = await jobApi.listJobs(filter);

      const items = (result.items || []).map((job: any) => {
        console.log('Processing job:', { id: job.id, status: job.status });
        return {
          ...job,
          proposals: job.proposals || { items: [], nextToken: undefined },
          proposalCount: job.proposals?.items?.length || 0,
          status: job.status || 'OPEN',
          isRemote: Boolean(job.isRemote),
          skills: Array.isArray(job.skills) ? job.skills : [],
          createdAt: job.createdAt || new Date().toISOString(),
          updatedAt: job.updatedAt || new Date().toISOString()
        };
      });

      return {
        items,
        nextToken: result.nextToken
      };
    } catch (error) {
      console.error('Error in listJobs:', error);
      return handleApiError('listJobs', error);
    }
  },

  async listMyJobs(clientId: string): Promise<Job[]> {
    try {
      const jobsResult = await this.listJobs({ clientId });
      const jobs = jobsResult?.items || [];

      return jobs.map(job => ({
        ...job,
        proposalCount: job.proposals?.items?.length || 0,
        proposals: {
          items: job.proposals?.items || [],
          nextToken: job.proposals?.nextToken
        }
      }));
    } catch (error) {
      console.error('Error in listMyJobs:', error);
      return handleApiError('listMyJobs', error);
    }
  },

  async submitProposal(
    input: CreateJobProposalInput,
    freelancerId?: string
  ): Promise<JobProposal> {
    try {
      if (!freelancerId) {
        throw new Error('Freelancer ID is required to submit a proposal');
      }

      const proposalInput = {
        jobId: input.jobId,
        freelancerId: freelancerId,
        coverLetter: input.coverLetter,
        bidAmount: input.bidAmount,
        proposedRate: input.proposedRate,
        status: 'PENDING' as const
      };

      return await jobApi.submitProposal(proposalInput);
    } catch (error) {
      console.error('Error in submitProposal:', error);
      return handleApiError('submitProposal', error);
    }
  },

  async applyForJob(input: CreateJobProposalInput): Promise<JobProposal> {
    console.warn('applyForJob is deprecated. Use submitProposal instead.');
    return this.submitProposal(input);
  },

  async getJobProposals(jobId: string): Promise<JobProposal[]> {
    try {
      const job = await this.getJob(jobId);
      return job.proposals || [];
    } catch (error) {
      return handleApiError('getJobProposals', error);
    }
  },

  getJobApplications: async function (jobId: string): Promise<JobProposal[]> {
    console.warn('getJobApplications is deprecated. Use getJobProposals instead.');
    return this.getJobProposals(jobId);
  },

  updateApplicationStatus: async function (
    id: string,
    status: ProposalStatus
  ): Promise<JobProposal> {
    console.warn('updateApplicationStatus is deprecated. Use updateProposalStatus instead.');
    return this.updateProposalStatus(id, status);
  },

  listMyApplications: async function (freelancerId: string): Promise<JobProposal[]> {
    console.warn('listMyApplications is deprecated. Use listMyProposals instead.');
    return this.listMyProposals(freelancerId);
  },

  async updateProposalStatus(
    id: string,
    status: ProposalStatus
  ): Promise<JobProposal> {
    try {
      return await jobApi.updateProposalStatus({ id, status });
    } catch (error) {
      console.error('Error in updateProposalStatus:', error);
      return handleApiError('updateProposalStatus', error);
    }
  },

  async listMyProposals(freelancerId: string): Promise<JobProposal[]> {
    try {
      const jobsResponse = await this.listJobs({ includeProposals: true });

      const proposals: JobProposal[] = [];

      for (const job of jobsResponse.items || []) {
        if (!job.proposals?.items) continue;

        const userProposal = job.proposals.items.find(
          (p: { freelancerId: string }) => p.freelancerId === freelancerId
        );

        if (userProposal) {
          let client = {
            id: job.clientId || '',
            name: 'Unknown Client',
            email: ''
          };

          if (job.client) {
            client = {
              id: job.client.id || job.clientId || '',
              name: job.client.name || 'Unknown Client',
              email: job.client.email || ''
            };
          }

          proposals.push({
            id: userProposal.id,
            jobId: job.id,
            freelancerId: userProposal.freelancerId,
            coverLetter: userProposal.coverLetter,
            bidAmount: userProposal.bidAmount,
            proposedRate: userProposal.proposedRate,
            status: userProposal.status as ProposalStatus,
            createdAt: userProposal.createdAt,
            updatedAt: userProposal.updatedAt,
            job: {
              ...job,
              client,
              title: job.title || 'Untitled Job',
              description: job.description || '',
              budget: job.budget || 0,
              status: job.status || 'OPEN',
              isRemote: Boolean(job.isRemote),
              skills: Array.isArray(job.skills) ? job.skills : [],
              createdAt: job.createdAt || new Date().toISOString(),
              updatedAt: job.updatedAt || new Date().toISOString()
            }
          });
        }
      }
      return proposals;
    } catch (error) {
      console.error('Error in listMyProposals:', error);
      return handleApiError('listMyProposals', error);
    }
  },


  async withdrawProposal(proposalId: string): Promise<void> {
    try {
      await jobApi.withdrawProposal(proposalId);
    } catch (error) {
      handleApiError('withdrawProposal', error);
    }
  },

  async withdrawApplication(proposalId: string): Promise<void> {
    console.warn('withdrawApplication is deprecated. Use withdrawProposal instead.');
    return this.withdrawProposal(proposalId);
  },

  /**
   * Checks if a freelancer has already submitted a proposal for a specific job
   * @param jobId - The ID of the job to check
   * @param freelancerId - The ID of the freelancer
   * @param job - Optional job object with proposals to check against
   * @returns boolean - True if the freelancer has already submitted a proposal for this job
   */
  hasSubmittedProposal(
    jobId: string,
    freelancerId: string,
    job?: {
      proposals?: {
        items?: Array<{
          freelancerId: string;
          jobId?: string;
        }>
      }
    }
  ): boolean {
    if (job?.proposals?.items) {
      return job.proposals.items.some(
        (proposal) => proposal.freelancerId === freelancerId &&
          (proposal.jobId ? proposal.jobId === jobId : true)
      );
    }

    console.warn('hasSubmittedProposal called without job proposals. Consider passing the job object for better performance.');
    return false;
  }
};
