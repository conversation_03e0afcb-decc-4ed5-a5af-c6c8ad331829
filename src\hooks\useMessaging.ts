import { useState, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import messageService from '@/api/messaging/message.service';

export const useMessaging = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Initiates a new conversation when a freelancer applies to a job
   */
  const startThreadFromApplication = useCallback(async (
    jobId: string,
    clientId: string,
    freelancerId: string,
  ) => {
    if (!user) {
      throw new Error('User must be authenticated to start a conversation');
    }

    setIsLoading(true);
    setError(null);

    try {
      await messageService.createConversation(jobId, clientId, freelancerId);
      
      window.location.href = `/messages?jobId=${jobId}&to=${freelancerId}`;
      
      return true;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to start conversation');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Initiates a new conversation when a client invites a freelancer
   */
  const startThreadFromInvitation = useCallback(async (
    jobId: string,
    clientId: string,
    freelancerId: string,
  ) => {
    if (!user) {
      throw new Error('User must be authenticated to start a conversation');
    }

    setIsLoading(true);
    setError(null);

    try {
      await messageService.createConversation(jobId, clientId, freelancerId);
      
      window.location.href = `/messages?jobId=${jobId}&to=${freelancerId}`;
      
      return true;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to start conversation');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  return {
    startThreadFromApplication,
    startThreadFromInvitation,
    isLoading,
    error,
  };
};
