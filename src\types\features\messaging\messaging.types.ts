import { UserProfile } from '../auth/auth.types';

export enum MessageStatus {
  SENDING = 'SENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  FAILED = 'FAILED'
}

export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  SYSTEM = 'SYSTEM'
}

export interface FileInfo {
  url: string;
  name: string;
  size?: number;
  type?: string;
  previewUrl?: string;
}

export interface BaseMessage {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  type: MessageType;
  status: MessageStatus;
  createdAt: string;
  updatedAt: string;
  fileInfo?: FileInfo;
  metadata?: Record<string, unknown>;
}

export interface Message extends BaseMessage {
  sender: Pick<UserProfile, 'id' | 'name' | 'avatar'>;
  isOwn: boolean;
}

export interface Conversation {
  id: string;
  participants: Pick<UserProfile, 'id' | 'name' | 'avatar' | 'role'>[];
  lastMessage?: Message;
  unreadCount: number;
  updatedAt: string;
  job?: {
    id: string;
    title: string;
    budget: number;
  };
}

export interface SendMessageDto {
  conversationId: string;
  content: string;
  type?: MessageType;
  file?: File;
  metadata?: Record<string, unknown>;
}

export interface CreateConversationDto {
  participantIds: string[];
  jobId?: string;
  initialMessage?: string;
}

export interface MessagesResponse {
  items: Message[];
  nextToken?: string;
  conversation: Conversation;
}

export interface MessageBubbleProps {
  message: Message;
  showAvatar?: boolean;
  showStatus?: boolean;
  className?: string;
}

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (conversationId: string) => void;
  loading?: boolean;
  emptyState?: React.ReactNode;
}

export interface MessageEvent {
  type: 'NEW_MESSAGE' | 'MESSAGE_UPDATED' | 'CONVERSATION_UPDATED';
  payload: Message | Conversation;
}

export interface MessagingState {
  conversations: Record<string, Conversation>;
  messages: Record<string, Message[]>;
  activeConversationId: string | null;
  isLoading: boolean;
  error: string | null;
}
