'use client';

import React, { useState, useMemo } from 'react';
import { Navbar } from '@/components/layout/Navbar';
import { Sidebar as SidebarComponent } from '@/components/layout/Sidebar';
import { Container } from '@/components/layout/Container';
import { BreadcrumbItem } from '@/components/ui/Breadcrumb';
import { cn } from '@/lib/utils';
import { useAuth } from '@/lib/auth/AuthContext';
import { usePathname } from 'next/navigation';
import { getSidebarItems, type UserRole } from '@/config/sidebar';

export interface DashboardLayoutProps {
  children: React.ReactNode;
  breadcrumbs?: BreadcrumbItem[];
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  showSidebar?: boolean;
  forceRole?: UserRole;
  proposalCount?: number;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  className,
  showSidebar = true,
  forceRole,
  proposalCount = 0,
}) => {
  const { user } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const pathname = usePathname();

  const sidebarItems = useMemo(() => {
    const role = forceRole || (user?.attributes?.['custom:role'] as UserRole) || 'CLIENT';
    return getSidebarItems(role, pathname, proposalCount);
  }, [user?.attributes, forceRole, pathname, proposalCount]);

  return (
    <div className="min-h-screen bg-background flex flex-col h-screen w-full">
      {/* Fixed Header */}
      <div className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <Navbar />
      </div>

      <div className="flex flex-1 overflow-hidden w-full">
        {/* Fixed Sidebar */}
        {showSidebar && (
          <div className={cn(
            "hidden lg:flex flex-col border-r bg-background h-[calc(100vh-4rem)] sticky top-16 flex-shrink-0 transition-all duration-300",
            sidebarCollapsed ? "w-16" : "w-64"
          )}>
            <SidebarComponent
              items={sidebarItems}
              className="flex-1 overflow-y-auto"
              collapsible={true}
              defaultCollapsed={sidebarCollapsed}
              onCollapseChange={setSidebarCollapsed}
            />
          </div>
        )}

        {/* Scrollable Content */}
        <main className={cn(
          'flex-1 overflow-y-auto w-full transition-all duration-300',
          className
        )}>
          <div className="min-h-[calc(100vh-4rem)] w-full">
            <Container className="py-6 lg:py-8 px-4 sm:px-6">
              {children}
            </Container>
          </div>
        </main>
      </div>
    </div>
  );
};

export { DashboardLayout };
