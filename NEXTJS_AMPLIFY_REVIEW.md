# Next.js with AWS Amplify - Key Recommendations

## 1. Authentication Service

### Error Handling
```typescript
export class AuthError extends Error {
  constructor(
    public code: string,
    message: string,
    public originalError?: unknown
  ) {
    super(message);
    this.name = 'AuthError';
  }
}
```

### Token Management
```typescript
private async ensureValidSession() {
  const session = await this.getCurrentSession();
  if (!session) throw new AuthError('no_session', 'No active session');
  
  const expiresIn = session.tokens?.accessToken?.payload.exp * 1000 - Date.now();
  if (expiresIn < 5 * 60 * 1000) {
    await this.refreshSession();
  }
}
```

## 2. Protected Routes

```typescript
interface ProtectedRouteProps {
  roles?: UserRole[];
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  roles = [], 
  children 
}) => {
  const { isAuthenticated, user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push(`/login?redirect=${encodeURIComponent(router.asPath)}`);
    } else if (!loading && isAuthenticated && roles.length > 0 && 
               user?.attributes?.['custom:role'] && 
               !roles.includes(user.attributes['custom:role'])) {
      router.push('/unauthorized');
    }
  }, [isAuthenticated, loading, router, roles, user]);

  if (loading) return <LoadingSpinner />;
  if (!isAuthenticated) return null;
  return <>{children}</>;
};
```

## 3. Performance

### Lazy Loading
```typescript
export const getAuthClient = () => 
  import('aws-amplify/auth').then(({ signIn, signOut }) => ({
    signIn,
    signOut,
  }));
```

## 4. State Management

```typescript
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ 
  children 
}) => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
  });

  const contextValue = useMemo(() => ({
    ...state,
  }), [state]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
```

## 5. Testing

```typescript
describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    authService = AuthService.getInstance();
    jest.clearAllMocks();
  });

  test('signIn with valid credentials', async () => {
    const mockSignIn = jest.fn().mockResolvedValue({
      isSignedIn: true,
    });
    
    jest.mock('aws-amplify/auth', () => ({
      signIn: mockSignIn,
    }));

    await expect(authService.signIn('<EMAIL>', 'password'))
      .resolves
      .toBeDefined();
  });
});
```

## 6. Security

```typescript
export const secureStorage = {
  set: (key: string, value: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, value);
    }
  },
  get: (key: string) => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(key);
    }
    return null;
  }
};
```

## 7. API Layer

```typescript
class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
      timeout: 30000,
    });
    this.setupInterceptors();
  }

  private setupInterceptors() {
    this.client.interceptors.request.use(
      async (config) => {
        const token = await authService.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
  }
}

export const apiClient = new ApiClient();
```

## 8. Environment Configuration

```typescript
export const config = {
  auth: {
    region: process.env.NEXT_PUBLIC_AWS_REGION,
    userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOL_ID,
    userPoolWebClientId: process.env.NEXT_PUBLIC_AWS_USER_POOL_WEB_CLIENT_ID,
  },
};
```

## 9. Error Boundary

```typescript
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <button onClick={() => window.location.reload()}>Reload</button>
        </div>
      );
    }
    return this.props.children;
  }
}
```

## 10. Custom Hooks

```typescript
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

## 11. Form Handling

```typescript
export const useForm = <T extends Record<string, any>>(
  initialValues: T,
  schema: ZodType
) => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<keyof T, string>>(
    {} as Record<keyof T, string>
  );

};
```

## 12. Monitoring

```typescript
export const initMonitoring = () => {
  if (process.env.NODE_ENV === 'production') {
    Sentry.init({
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 0.1,
    });
  }
};
```

## 13. Testing Utilities

```typescript
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  );
};
```

## Key Recommendations Summary

1. **Authentication**: Implement proper error handling and token refresh logic
2. **Authorization**: Use role-based access control for protected routes
3. **Performance**: Lazy load non-critical components and Amplify modules
4. **State Management**: Use React Context with useMemo for optimal performance
5. **Testing**: Write comprehensive unit tests for critical paths
6. **Security**: Secure storage and proper token handling
7. **Error Handling**: Implement global error boundaries and logging
8. **Type Safety**: Use TypeScript types consistently
9. **Documentation**: Add JSDoc comments for public APIs
10. **Monitoring**: Set up error tracking and performance monitoring

This document provides a solid foundation for building a maintainable and scalable Next.js application with AWS Amplify. Each section includes practical code examples that you can adapt to your specific needs.
