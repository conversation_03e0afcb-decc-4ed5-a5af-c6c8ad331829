import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'myvillagefreelance3cce9a8f06494a77a4705895aabe181757-local.s3.us-east-1.amazonaws.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'myvillagefreelance3cce9a8f06494a77a4705895aabe181757-local.s3.amazonaws.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'myvillagefreelance3cce9a8f06494a77a4705895aabe181757-local.s3.us-east-1.amazonaws.com',
        port: '',
        pathname: '/public/profile-photos/**',
      },
    ],
  },
};

export default nextConfig;
