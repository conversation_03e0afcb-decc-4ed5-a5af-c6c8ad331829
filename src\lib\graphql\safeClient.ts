import { generateClient } from 'aws-amplify/api';
import { fetchAuthSession } from 'aws-amplify/auth';
import type { DocumentNode } from '@apollo/client';
import { print } from 'graphql';

type SafeGraphQLAuthMode = 'API_KEY' | 'AMAZON_COGNITO_USER_POOLS';

interface GraphQLResult<T = unknown> {
  data?: T;
  errors?: Array<{ message: string }>;
}

interface AmplifyGraphQLClient {
  graphql: (options: {
    query: string;
    variables?: Record<string, unknown>;
    authMode?: 'apiKey' | 'userPool';
    authToken?: string;
  }) => Promise<GraphQLResult<unknown>>;
}

/**
 * A type-safe wrapper around AWS Amplify's GraphQL client
 */
export class SafeGraphQLClient {
  private client: AmplifyGraphQLClient;
  
  constructor() {
    this.client = generateClient() as AmplifyGraphQLClient;
  }

  /**
   * Execute a GraphQL query or mutation
   */
  async execute<T = unknown, V extends Record<string, unknown> = Record<string, unknown>>(options: {
    query: string | DocumentNode;
    variables?: V;
    authMode?: SafeGraphQLAuthMode;
  }): Promise<T> {
    try {
      const { query, variables, authMode = 'AMAZON_COGNITO_USER_POOLS' } = options;
      
      let authToken: string | undefined;
      if (authMode === 'AMAZON_COGNITO_USER_POOLS') {
        try {
          const session = await fetchAuthSession();
          authToken = session.tokens?.idToken?.toString();
        } catch (error) {
          console.warn('Failed to get auth token:', error);
        }
      }

      const queryString = typeof query === 'string' ? query : print(query);
      
      const graphQLOptions = {
        query: queryString,
        variables: variables as Record<string, unknown> | undefined,
        authMode: authMode === 'API_KEY' ? 'apiKey' as const : 'userPool' as const,
        ...(authToken && { authToken })
      };

      const result = await this.client.graphql(graphQLOptions) as GraphQLResult<T>;

      if (!('data' in result)) {
        throw new Error('Invalid response from GraphQL API: missing data');
      }

      if (result.errors && result.errors.length > 0) {
        const errorMessages = result.errors.map(e => e.message).join('\n');
        throw new Error(`GraphQL errors: ${errorMessages}`);
      }

      return result.data as T;
    } catch (error) {
      console.error('GraphQL execution error:', error);
      throw error;
    }
  }
}

export const safeClient = new SafeGraphQLClient();
