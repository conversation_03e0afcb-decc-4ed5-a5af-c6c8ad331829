"use client";
import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { jobService } from '@/api/jobs/job.service';
import type { JobProposal } from '@/types/proposal';

export interface ProposalCounts {
  total: number;
  pending: number;
  accepted: number;
  rejected: number;
}

interface UseProposalCountProps {
  initialProposals?: JobProposal[];
}

export function useProposalCount({ initialProposals }: UseProposalCountProps = {}) {
  const { user, isAuthenticated, cognitoUserId } = useAuth();
  const userRole = user?.attributes?.['custom:role'];
  const isFreelancer = userRole === 'FREELANCER';
  const [counts, setCounts] = useState<ProposalCounts>({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const initialProposalsRef = useRef(initialProposals);
  const isMounted = useRef(false);

  const calculateCounts = useCallback((proposals: any[] = []) => ({
    total: proposals.length,
    pending: proposals.filter(p => p.status === 'PENDING').length,
    accepted: proposals.filter(p => p.status === 'ACCEPTED').length,
    rejected: proposals.filter(p => p.status === 'REJECTED').length,
  }), []);

  useEffect(() => {
    if (isMounted.current) {
      initialProposalsRef.current = initialProposals;
      if (initialProposals?.length) {
        setCounts(calculateCounts(initialProposals));
      }
    }
  }, [initialProposals, calculateCounts]);

  const fetchProposalCounts = useCallback(async (forceRefresh = false) => {
    if (!isMounted.current) return;
    
    if (!isAuthenticated || !cognitoUserId || !isFreelancer) {
      if (isMounted.current) {
        setLoading(false);
      }
      return;
    }

    if (initialProposalsRef.current?.length && !forceRefresh) {
      if (isMounted.current) {
        const calculated = calculateCounts(initialProposalsRef.current);
        setCounts(calculated);
        setLoading(false);
      }
      return;
    }

    try {
      if (isMounted.current) {
        setLoading(true);
        setError(null);
      }
      
      const proposals = await jobService.listMyProposals(cognitoUserId);
      
      if (isMounted.current) {
        const calculated = calculateCounts(proposals);
        setCounts(calculated);
      }
    } catch (err) {
      console.error('Error fetching proposal counts:', err);
      if (isMounted.current) {
        setError(err instanceof Error ? err.message : 'Failed to fetch proposal counts');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [isAuthenticated, cognitoUserId, isFreelancer, calculateCounts]);

  useEffect(() => {
    isMounted.current = true;

    if (isAuthenticated && isFreelancer && cognitoUserId) {
      const fetchInitialData = async () => {
        if (initialProposalsRef.current?.length) {
          const calculated = calculateCounts(initialProposalsRef.current);
          if (isMounted.current) {
            setCounts(calculated);
            setLoading(false);
          }
        } else {
          await fetchProposalCounts();
        }
      };

      fetchInitialData();
    } else {
      if (isMounted.current) {
        setLoading(false);
      }
    }

    return () => {
      isMounted.current = false;
    };
  }, [fetchProposalCounts, isAuthenticated, isFreelancer, cognitoUserId, calculateCounts]);

  const refresh = useCallback(async (updatedProposals?: JobProposal[]) => {
    if (!isMounted.current || !isAuthenticated || !cognitoUserId || !isFreelancer) {
      return;
    }

    try {
      setError(null);
      
      if (updatedProposals) {
        setCounts(calculateCounts(updatedProposals));
      } else {
        await fetchProposalCounts(true);
      }
    } catch (err) {
      console.error('Error refreshing proposal counts:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh proposal counts');
    }
  }, [isAuthenticated, cognitoUserId, fetchProposalCounts, calculateCounts, isFreelancer]);

  return {
    counts,
    loading,
    error,
    refresh,
  };
}
