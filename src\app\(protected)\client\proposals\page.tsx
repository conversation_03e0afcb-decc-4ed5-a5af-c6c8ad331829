"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { Button } from "@/components/ui";
import { Card, CardContent } from "@/components/ui/Card";
import { Table, Column } from "@/components/ui/Table";
import { Icon } from "@/components/ui";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { formatDistanceToNow } from "date-fns";
import type { TableData } from "@/types/components/Table";
import { Badge } from "@/components/ui/Badge";
import Link from "next/link";
import { graphQLClient } from "@/lib/graphql/graphqlClient";
import { gql } from "@apollo/client";

interface ProposalWithJob extends TableData {
  id: string;
  jobId: string;
  freelancerId: string;
  freelancerName?: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate?: number;
  status: string;
  createdAt: string;
  updatedAt?: string;
  job: {
    id: string;
    title: string;
    budget: number;
    status: string;
  };
  freelancer: {
    id: string;
    name: string;
    email: string;
  };
}

const ITEMS_PER_PAGE = 10;

const ClientProposalsPage = () => {
  const router = useRouter();
  const { isAuthenticated, user, loading: authLoading, cognitoUserId, isInitialized } = useAuth();
  const [proposals, setProposals] = useState<ProposalWithJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    sortBy: "newest",
  });

  const fetchProposals = useCallback(async () => {
    const userIdentifier = cognitoUserId;
    if (!userIdentifier) return;

    try {
      setIsLoading(true);
      
      const LIST_PROPOSALS = gql`
        query ListProposals {
          listProposals {
            items {
              id
              jobId
              freelancerId
              bidAmount
              coverLetter
              status
              proposedRate
              createdAt
              updatedAt
              freelancer {
                id
                name
                email
              }
              job {
                id
                title
                budget
                status
              }
            }
          }
        }
      `;
      
      interface ListProposalsResponse {
        listProposals: {
          items: Array<{
            id: string;
            jobId: string;
            freelancerId: string;
            coverLetter: string;
            status: string;
            bidAmount: number;
            proposedRate?: number;
            createdAt: string;
            updatedAt?: string;
            job: {
              id: string;
              title: string;
              description: string;
              category: string;
              budget: number;
              status: string;
            };
            freelancer: {
              id: string;
              name: string;
              email: string;
            };
          }>;
        };
      }

      const response = await graphQLClient.query<ListProposalsResponse>(LIST_PROPOSALS);
      const allProposals = response.listProposals?.items || [];
      
      const proposalsWithDetails = allProposals.map((proposal) => {
        const job = proposal.job || {
          id: proposal.jobId,
          title: `Job ${proposal.jobId?.slice(0, 6) || 'N/A'}`,
          budget: 0,
          status: 'UNKNOWN'
        };

        const freelancer = proposal.freelancer || {
          id: proposal.freelancerId || 'unknown',
          name: `Freelancer ${proposal.freelancerId?.slice(0, 6) || 'N/A'}`,
          email: ''
        };

        return {
          id: proposal.id,
          jobId: proposal.jobId,
          freelancerId: proposal.freelancerId,
          coverLetter: proposal.coverLetter || "",
          bidAmount: proposal.bidAmount || 0,
          proposedRate: proposal.proposedRate,
          status: proposal.status || "PENDING",
          createdAt: proposal.createdAt,
          updatedAt: proposal.updatedAt,
          job: {
            id: job.id,
            title: job.title,
            budget: job.budget || 0,
            status: job.status || 'UNKNOWN'
          },
          freelancer: {
            id: freelancer.id,
            name: freelancer.name,
            email: freelancer.email || ''
          }
        } as ProposalWithJob;
      });

      setProposals(proposalsWithDetails);
    } catch (error) {
      console.error("Error fetching proposals:", error);
    } finally {
      setIsLoading(false);
    }
  }, [cognitoUserId]);

  useEffect(() => {
    const userRole = user?.attributes?.['custom:role'] || 'CLIENT';

  const shouldCallFetchProposals = isAuthenticated &&
                  userRole === 'CLIENT' &&
                  !authLoading &&
                  isInitialized &&
                  Boolean(cognitoUserId);

    if (shouldCallFetchProposals) {
      fetchProposals();
    }
  }, [isAuthenticated, user, fetchProposals, authLoading, isInitialized, cognitoUserId]);

  const filteredProposals = proposals
    .filter((proposal) => {
      const matchesSearch =
        !searchTerm ||
        proposal.job?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proposal.freelancer?.name
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        proposal.coverLetter?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus =
        !filters.status || proposal.status === filters.status;

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (filters.sortBy) {
        case "oldest":
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case "budget_high":
          return (b.bidAmount || 0) - (a.bidAmount || 0);
        case "budget_low":
          return (a.bidAmount || 0) - (b.bidAmount || 0);
        case "newest":
        default:
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
      }
    });

  const totalPages = Math.ceil(filteredProposals.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const currentProposals = filteredProposals.slice(
    startIndex,
    startIndex + ITEMS_PER_PAGE
  );

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "secondary" as const, label: "Pending" },
      ACCEPTED: { variant: "success" as const, label: "Accepted" },
      REJECTED: { variant: "destructive" as const, label: "Rejected" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "secondary" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const columns: Column<ProposalWithJob>[] = [
    {
      header: "Job Title",
      accessor: (row: ProposalWithJob) => row.job?.title || `Job ${row.jobId?.slice(0, 6) || 'N/A'}`,
      cell: (_value: unknown, row: ProposalWithJob) => (
        <div className="font-medium">
          <Link
            href={`/client/jobs/${row.jobId}`}
            className="text-primary hover:underline flex items-center gap-2"
          >
            <Icon
              name="Briefcase"
              size="sm"
              className="text-muted-foreground"
            />
            <span>{row.job?.title || "Unknown Job"}</span>
          </Link>
        </div>
      ),
    },
    {
      header: "Freelancer",
      accessor: (row: ProposalWithJob) => row.freelancer?.name || 'Unknown',
      cell: (_value: unknown, row: ProposalWithJob) => (
        <div className="flex items-center gap-2">
          <Icon name="User" size="sm" className="text-muted-foreground" />
          <div>
            <div className="font-medium">
              {row.freelancer?.name || "Unknown Freelancer"}
            </div>
            {row.freelancer?.email && (
              <div className="text-xs text-muted-foreground">
                {row.freelancer.email}
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      header: "Bid Amount",
      accessor: "bidAmount",
      cell: (value: unknown) => (
        <div className="flex items-center gap-2">
          <Icon name="DollarSign" size="sm" className="text-muted-foreground" />
          <span className="font-medium">
            ${(value as number)?.toLocaleString() || 0}
          </span>
        </div>
      ),
    },
    {
      header: "Status",
      accessor: "status",
      cell: (value: unknown) => getStatusBadge(value as string),
    },
    {
      header: "Submitted",
      accessor: "createdAt",
      cell: (value: unknown) => (
        <div className="flex items-center gap-2">
          <Icon name="Calendar" size="sm" className="text-muted-foreground" />
          <span className="text-muted-foreground">
            {formatDistanceToNow(new Date(value as string), {
              addSuffix: true,
            })}
          </span>
        </div>
      ),
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (_value: unknown, row: ProposalWithJob) => (
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/client/proposals/${row.id}`);
            }}
            title="View details"
          >
            <Icon name="Eye" size="sm" />
          </Button>
        </div>
      ),
    },
  ];

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="Proposals Received"
          subtitle="Manage proposals from freelancers for your job postings"
          breadcrumbs={[
            { label: 'Dashboard', href: '/client/dashboard' },
            { label: 'Proposals', current: true }
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button 
            size="sm"
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name={showFilters ? "FilterX" : "Filter"} size="sm" />
            <span>{showFilters ? 'Hide Filters' : 'Filters'}</span>
          </Button>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col gap-4">
        {showFilters && (
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 gap-4">
                {/* Search Input */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Icon name="Search" size="sm" className="text-muted-foreground" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search by job title, freelancer name, or cover letter..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="pl-10 w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        </div>

        {/* Advanced Filters */}
                  <div>
                    <label className="block text-sm font-medium text-muted-foreground mb-1">
                      Status
                    </label>
                  <select
                    value={filters.status}
                    onChange={(e) =>
                      setFilters({ ...filters, status: e.target.value })
                    }
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">All Statuses</option>
                    <option value="PENDING">Pending</option>
                    <option value="ACCEPTED">Accepted</option>
                    <option value="REJECTED">Rejected</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Sort By
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) =>
                      setFilters({ ...filters, sortBy: e.target.value })
                    }
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="budget_high">
                      Bid Amount (High to Low)
                    </option>
                    <option value="budget_low">Bid Amount (Low to High)</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

      </div>

      {/* Proposals Table */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Icon name="Loader2" size="lg" className="animate-spin" />
            </div>
          ) : (
            <Table<ProposalWithJob>
              columns={columns}
              data={currentProposals}
              emptyState={{
                title: "No proposals found",
                description:
                  searchTerm || filters.status
                    ? "Try adjusting your search or filter criteria."
                    : "You haven't received any proposals yet.",
                icon: "FileText",
              }}
              pagination={{
                enabled: true,
                currentPage,
                totalPages,
                onPageChange: (page: number) => setCurrentPage(page),
                pageSize: ITEMS_PER_PAGE,
                totalItems: filteredProposals.length,
              }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientProposalsPage;
