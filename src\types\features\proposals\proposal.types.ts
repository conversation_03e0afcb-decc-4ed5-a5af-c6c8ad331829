import { ApiResponse, PaginatedResponse } from '@/types/common/api.types';

export enum ProposalStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN'
}

export interface BaseProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  status: ProposalStatus;
  createdAt: string;
  updatedAt: string;
  proposedRate?: number;
  estimatedTime?: string;
}

export interface Proposal extends BaseProposal {
  job?: {
    id: string;
    title: string;
    budget: number;
    status: string;
  };
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
  };
}

export interface CreateProposalDto {
  jobId: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate?: number;
  estimatedTime?: string;
}

export interface UpdateProposalDto extends Partial<CreateProposalDto> {
  status?: ProposalStatus;
  reason?: string;
}

export type ProposalsResponse = PaginatedResponse<Proposal>;
export type ProposalResponse = ApiResponse<Proposal>;

export interface ProposalCardProps {
  proposal: Proposal;
  onAction?: (proposal: Proposal) => void;
  actionLabel?: string;
  className?: string;
}

export interface ProposalFormProps {
  initialData?: Partial<Proposal>;
  onSubmit: (data: CreateProposalDto | UpdateProposalDto) => Promise<void>;
  isSubmitting?: boolean;
  submitLabel?: string;
}
