"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { DatePicker } from "@/components/ui/DatePicker";
import { Form, FormField } from "@/components/ui/Form";
import { Select } from "@/components/ui/Select";
import { Contract, ContractStatus, ContractType, CreateContractDto } from "@/types/features/contracts/contract.types";

const contractFormSchema = yup.object().shape({
  title: yup.string().required("Title is required"),
  description: yup.string().required("Description is required"),
  type: yup.string().required("Type is required"),
  terms: yup.string().required("Terms are required"),
  startDate: yup.date().required("Start date is required"),
  endDate: yup
    .date()
    .optional()
    .when("startDate", (startDate: any) => {
      if (!startDate) return yup.date().optional();
      return yup.date().min(startDate, "End date must be after start date");
    }),
  budget: yup
    .number()
    .min(0.01, "Budget must be greater than 0")
    .required("Budget is required"),
  clientId: yup.string().required("Client is required"),
  freelancerId: yup.string().required("Freelancer is required"),
  jobId: yup.string().optional(),
  proposalId: yup.string().optional(),
});

type ContractFormValues = yup.InferType<typeof contractFormSchema> & {
  endDate?: Date | null;
};

interface ContractFormProps {
  initialData?: Partial<Contract> & { jobId?: string; proposalId?: string };
  onSubmit: (data: CreateContractDto) => Promise<void>;
  isLoading?: boolean;
  submitButtonText?: string;
  className?: string;
}

export const ContractForm: React.FC<ContractFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  submitButtonText = "Create Contract",
  className = "",
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContractFormValues>({
    resolver: yupResolver(contractFormSchema as any),
    defaultValues: {
      title: initialData?.title || "",
      description: initialData?.description || "",
      type: initialData?.type || ContractType.FIXED_PRICE,
      terms: initialData?.terms || "",
      clientId: initialData?.clientId || "",
      freelancerId: initialData?.freelancerId || "",
      startDate: initialData?.startDate
        ? new Date(initialData.startDate)
        : new Date(),
      endDate: initialData?.endDate ? new Date(initialData.endDate) : undefined,
      budget: initialData?.budget || 0,
    },
  });

  const { register, control, formState: { errors }, watch } = form;
  const startDate = watch("startDate");

  const handleSubmit = async (data: ContractFormValues) => {
    console.log('Form submission started with data:', JSON.stringify(data, null, 2));
    try {
      setIsSubmitting(true);

      const contractData: CreateContractDto = {
        jobId: initialData?.jobId || "",
        proposalId: initialData?.proposalId || "",
        title: data.title,
        description: data.description,
        type: data.type as ContractType,
        status: ContractStatus.DRAFT, // Default status when creating a new contract
        terms: data.terms,
        startDate: data.startDate.toISOString(),
        endDate: data.endDate?.toISOString(),
        budget: data.budget,
        clientId: data.clientId,
        freelancerId: data.freelancerId,
      };

      console.log('Sending contract data to onSubmit handler:', JSON.stringify(contractData, null, 2));
      await onSubmit(contractData);
      console.log('Form submission successful');
    } catch (error) {
      console.error("Error submitting contract:", error);
      if (error instanceof Error) {
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };


  return (
    <Form onSubmit={form.handleSubmit(handleSubmit)}>
      <div className={`space-y-6 ${className}`}>
        {/* Hidden fields for required IDs */}
        <input type="hidden" {...register('clientId')} />
        <input type="hidden" {...register('freelancerId')} />
        
        {/* Display form-wide errors */}
        {(errors.clientId || errors.freelancerId) && (
          <div className="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">
            <div className="font-medium">Please fix the following errors:</div>
            <ul className="mt-1.5 list-disc list-inside">
              {errors.clientId && <li>{errors.clientId.message as string}</li>}
              {errors.freelancerId && <li>{errors.freelancerId.message as string}</li>}
            </ul>
          </div>
        )}
        
        <div className="space-y-4">
          <FormField
            label="Contract Title"
            required
            error={errors.title?.message as string}
          >
            <Input
              placeholder="e.g., Website Development Contract"
              {...register('title')}
            />
          </FormField>

          <FormField
            label="Description"
            required
            error={errors.description?.message as string}
          >
            <Textarea
              placeholder="Describe the work to be performed..."
              className="min-h-[120px]"
              {...register('description')}
            />
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Contract Type" required error={errors.type?.message as string}>
              <Select
                {...register('type')}
                options={[
                  { value: ContractType.FIXED_PRICE, label: 'Fixed Price' },
                  { value: ContractType.HOURLY, label: 'Hourly' },
                ]}
                placeholder="Select contract type"
                className="w-full"
              />
            </FormField>

            <FormField label="Total Budget ($)" required error={errors.budget?.message as string}>
              <Input
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.00"
                {...register('budget', { valueAsNumber: true })}
              />
            </FormField>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Start Date" required error={errors.startDate?.message as string}>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start date"
                    minDate={new Date()}
                  />
                )}
              />
            </FormField>

            <FormField label="End Date (Optional)" error={errors.endDate?.message as string}>
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    value={field.value || undefined}
                    onChange={field.onChange}
                    placeholder="Select end date (optional)"
                    minDate={startDate}
                  />
                )}
              />
            </FormField>
          </div>

          <FormField
            label="Terms & Conditions"
            error={errors.terms?.message as string}
          >
            <Textarea
              placeholder="Specify the terms and conditions of this contract..."
              className="min-h-[150px] font-mono text-sm"
              {...register('terms')}
            />
            <p className="text-sm text-muted-foreground mt-2">
              Be clear about payment terms, revision policies, ownership
              rights, and any other important conditions.
            </p>
          </FormField>
        </div>
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            disabled={isSubmitting || isLoading}
            onClick={() => {
              console.log('Save Draft button clicked');
              console.log('Current form values:', form.getValues());
              // TODO: Implement save as draft functionality
              alert('Save as draft functionality not implemented yet');
            }}
          >
            Save Draft
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting || isLoading}
            onClick={() => {
              console.log('Submit button clicked');
              console.log('Form validation errors:', form.formState.errors);
            }}
          >
            {isSubmitting || isLoading ? "Saving..." : submitButtonText}
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default ContractForm;
