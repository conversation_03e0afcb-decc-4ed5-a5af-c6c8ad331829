import { useState, useCallback } from 'react';

export function useMessagingDrawer(initialState = false) {
  const [isOpen, setIsOpen] = useState(initialState);

  const openDrawer = useCallback(() => {
    setIsOpen(true);
    document.body.style.overflow = 'hidden';
  }, []);

  const closeDrawer = useCallback(() => {
    setIsOpen(false);
    document.body.style.overflow = '';
  }, []);

  const toggleDrawer = useCallback(() => {
    setIsOpen(prev => {
      const newState = !prev;
      document.body.style.overflow = newState ? 'hidden' : '';
      return newState;
    });
  }, []);

  const useDrawerEffect = () => {
    return () => {
      document.body.style.overflow = '';
    };
  };

  return {
    isOpen,
    openDrawer,
    closeDrawer,
    toggleDrawer,
    useDrawerEffect,
  };
}
