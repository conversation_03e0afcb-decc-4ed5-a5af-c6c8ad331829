'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { But<PERSON> } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { UserRole } from '@/config/sidebar';

const SidebarTestPage = () => {
  const [currentRole, setCurrentRole] = useState<UserRole>('FREELANCER');
  
  return (
    <DashboardLayout 
      title="Sidebar Test" 
      description="Test the sidebar with different user roles"
      forceRole={currentRole}
    >
      <Card>
        <CardHeader>
          <CardTitle>Sidebar Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Test Different Roles</h3>
            <div className="flex flex-wrap gap-2">
              {['CLIENT', 'FREELANCER', 'ADMIN'].map((role) => (
                <Button
                  key={role}
                  variant={currentRole === role ? 'default' : 'outline'}
                  onClick={() => setCurrentRole(role as UserRole)}
                >
                  {role}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Current Role: {currentRole}</h3>
            <p className="text-muted-foreground">
              The sidebar should update automatically when you change the role.
            </p>
          </div>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default SidebarTestPage;
