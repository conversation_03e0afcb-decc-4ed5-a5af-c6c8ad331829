import { ReactNode } from 'react';
import { IconName } from './Icon';

export type AccessorFn<T, R = unknown> = (row: T) => R;
export type CellRenderer<T, V = unknown> = (value: V, row: T) => ReactNode;

export interface TableData {
  id: string | number;
  [key: string]: string | number | boolean | null | undefined | Record<string, unknown>;
}

export interface Column<T> {
  /** Column header content */
  header: string | ReactNode;
  /** Key to access data in the row object or a function to derive the value */
  accessor: keyof T | AccessorFn<T>;
  /** Custom cell renderer function */
  cell?: CellRenderer<T>;
  /** Additional CSS classes for the column */
  className?: string;
  /** Additional CSS classes for the column header */
  headerClassName?: string;
  /** Additional CSS classes for the column cells */
  cellClassName?: string | ((row: T) => string);
  /** Whether the column is sortable */
  sortable?: boolean;
  /** Width of the column */
  width?: string | number;
  /** Minimum width of the column */
  minWidth?: string | number;
  /** Maximum width of the column */
  maxWidth?: string | number;
  /** Whether the column is resizable */
  resizable?: boolean;
  /** Whether the column is visible */
  visible?: boolean;
  /** Callback when the column is clicked */
  onHeaderClick?: (column: Column<T>) => void;
  /** Whether the column is sticky */
  sticky?: boolean | 'left' | 'right';
  /** Custom header renderer */
  headerRenderer?: (column: Column<T>) => ReactNode;
}

export interface TableProps<T> {
  /** Array of column configurations */
  columns: Column<T>[];
  /** Array of data objects to display in the table */
  data: T[];
  /** Callback when a row is clicked */
  onRowClick?: (row: T, event: React.MouseEvent) => void;
  /** Callback when a cell is clicked */
  onCellClick?: (row: T, column: Column<T>, event: React.MouseEvent) => void;
  /** Configuration for the empty state */
  emptyState?: {
    /** Title to display when there's no data */
    title: string;
    /** Optional description */
    description?: string | ReactNode;
    /** Optional icon name */
    icon?: IconName;
    /** Optional action button */
    action?: ReactNode;
    /** Additional CSS classes */
    className?: string;
  };
  /** Title of the table */
  title?: string | ReactNode;
  /** Optional description */
  description?: string | ReactNode;
  /** Optional icon name */
  icon?: IconName;
  /** Optional action buttons */
  action?: ReactNode;
  /** Additional CSS classes for the table */
  className?: string;
  /** Additional CSS classes for the table header */
  headerClassName?: string;
  /** Additional CSS classes for the table body */
  bodyClassName?: string;
  /** Additional CSS classes for table rows, or a function that returns classes based on row data */
  rowClassName?: string | ((row: T, index: number) => string);
  /** Whether the table is in a loading state */
  isLoading?: boolean;
  /** Number of skeleton rows to show when loading */
  loadingRows?: number;
  /** Key to use as a unique identifier for each row (defaults to 'id') */
  rowKey?: keyof T | ((row: T) => string | number);
  /** Sorting configuration */
  sortConfig?: {
    /** Current sort key */
    key: keyof T | null;
    /** Current sort direction */
    direction: 'asc' | 'desc';
    /** Callback when sort changes */
    onSort: (key: keyof T) => void;
  };
  /** Whether to show the header */
  showHeader?: boolean;
  /** Whether to show the footer */
  showFooter?: boolean;
  /** Footer content */
  footer?: ReactNode | ((rows: T[]) => ReactNode);
  /** Pagination configuration */
  pagination?: {
    /** Whether pagination is enabled */
    enabled: boolean;
    /** Current page number (1-based) */
    currentPage: number;
    /** Total number of items */
    totalItems?: number;
    /** Number of items per page */
    pageSize: number;
    /** Total number of pages */
    totalPages: number;
    /** Callback when page changes */
    onPageChange: (page: number) => void;
    /** Whether to show first/last page buttons */
    showFirstLast?: boolean;
    /** Whether to show previous/next buttons */
    showPrevNext?: boolean;
    /** Maximum number of visible page numbers */
    maxVisiblePages?: number;
    /** Custom class name for pagination */
    className?: string;
  };
  /** Whether to show row hover effects */
  hoverable?: boolean;
  /** Whether to show row borders */
  bordered?: boolean;
  /** Whether to use a compact layout */
  compact?: boolean;
  /** Whether the table is selectable */
  selectable?: boolean;
  /** Currently selected rows */
  selectedRows?: T[];
  /** Callback when selected rows change */
  onSelectionChange?: (selectedRows: T[]) => void;
  /** Key to use for row selection (defaults to 'id') */
  selectionKey?: keyof T;
  /** Whether to show checkboxes for row selection */
  showSelectAll?: boolean;
  /** Custom row renderer */
  rowRenderer?: (row: T, index: number) => ReactNode;
  /** Custom empty state renderer */
  emptyRenderer?: () => ReactNode;
  /** Custom loading state renderer */
  loadingRenderer?: () => ReactNode;
  /** Whether to use a virtualized list for large datasets */
  virtualized?: boolean;
  /** Height of the table when virtualized */
  height?: number | string;
  /** Row height when virtualized */
  rowHeight?: number;
  /** Number of rows to render outside the visible area when virtualized */
  overscanRowCount?: number;
  /** Callback when the table is scrolled */
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
  /** Callback when more rows need to be loaded (for infinite scroll) */
  onLoadMore?: () => void;
  /** Whether there are more rows to load */
  hasMore?: boolean;
  /** Loading component to show when loading more rows */
  loadingMoreIndicator?: ReactNode;
  /** Error state */
  error?: string | Error | null;
  /** Callback to retry loading data */
  onRetry?: () => void;
}
