import { NextRequest, NextResponse } from 'next/server';

const ROLE_ROUTES = {
  '/client': 'CLIENT',
  '/freelancer': 'FREELANCER', 
  '/admin': 'ADMIN'
} as const;

const PUBLIC_ROUTES = [
  '/login',
  '/signup',
  '/forgot-password',
  '/landing',
  '/design-system-demo',
  '/_next',
  '/api',
  '/favicon.ico'
];

const PROTECTED_ROUTES = [
  '/client',
  '/freelancer', 
  '/admin',
  '/profile',
  '/settings',
  '/jobs',
  '/contracts'
];

/**
 * Check if the user is authenticated by looking for auth tokens in cookies
 * This checks for AWS Amplify Cognito tokens
 */
function isAuthenticated(request: NextRequest): boolean {
  const cookies = request.cookies;

  // Look for Amplify Cognito auth tokens
  // Amplify stores tokens with patterns like:
  // CognitoIdentityServiceProvider.{userPoolClientId}.{username}.accessToken
  // CognitoIdentityServiceProvider.{userPoolClientId}.{username}.idToken
  // Also check for other Amplify auth-related cookies
  for (const [name] of cookies) {
    if (name.includes('CognitoIdentityServiceProvider')) {
      if (name.includes('.accessToken') || name.includes('.idToken') || name.includes('.refreshToken')) {
        const tokenValue = cookies.get(name)?.value;
        if (tokenValue && tokenValue.length > 0 && tokenValue !== 'undefined' && tokenValue !== 'null') {
          return true;
        }
      }
    }
    if (name.includes('amplify-signin') || name.includes('LastAuthUser')) {
      const tokenValue = cookies.get(name)?.value;
      if (tokenValue && tokenValue.length > 0 && tokenValue !== 'undefined' && tokenValue !== 'null') {
        return true;
      }
    }
  }

  return false;
}

/**
 * Extract user role from cookies/tokens
 * This attempts to decode the JWT token to get the custom:role attribute
 */
function getUserRole(request: NextRequest): string | null {
  const cookies = request.cookies;

  for (const [name] of cookies) {
    if (name.includes('CognitoIdentityServiceProvider') && name.includes('.idToken')) {
      const tokenValue = cookies.get(name)?.value;
      if (tokenValue && tokenValue !== 'undefined' && tokenValue !== 'null') {
        try {
          const parts = tokenValue.split('.');
          if (parts.length === 3) {
            const payload = JSON.parse(atob(parts[1]));
            const role = payload['custom:role'];
            if (role && ['CLIENT', 'FREELANCER', 'ADMIN'].includes(role)) {
              return role;
            }
          }
        } catch (error) {
          console.error('Error decoding token:', error);
        }
      }
    }
  }

  return null;
}

/**
 * Get the appropriate dashboard path for a user role
 */
function getDashboardPath(role: string): string {
  switch (role.toUpperCase()) {
    case 'CLIENT':
      return '/client/dashboard';
    case 'FREELANCER':
      return '/freelancer/dashboard';
    case 'ADMIN':
      return '/admin/dashboard';
    default:
      return '/login';
  }
}

/**
 * Check if a path is public (doesn't require authentication)
 */
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => pathname.startsWith(route));
}

/**
 * Check if a path requires authentication
 */
function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route));
}

/**
 * Get the required role for a protected route
 */
function getRequiredRole(pathname: string): string | null {
  for (const [route, role] of Object.entries(ROLE_ROUTES)) {
    if (pathname.startsWith(route)) {
      return role;
    }
  }
  return null;
}

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  if (pathname.startsWith('/_next') ||
      pathname.startsWith('/api') ||
      pathname.includes('.') ||
      searchParams.has('_rsc')) {
    return NextResponse.next();
  }

  const isNextInternalRequest = request.headers.get('rsc') === '1' ||
                               request.headers.get('next-router-prefetch') === '1' ||
                               request.headers.get('purpose') === 'prefetch' ||
                               request.headers.get('x-middleware-prefetch') === '1' ||
                               request.headers.get('accept')?.includes('text/x-component');

  if (isNextInternalRequest) {
    return NextResponse.next();
  }

  const authenticated = isAuthenticated(request);
  const userRole = authenticated ? getUserRole(request) : null;

  console.log(`[Middleware] ${pathname} - Auth: ${authenticated}, Role: ${userRole}`);
  
  if (pathname === '/') {
    if (authenticated && userRole) {
      const dashboardPath = getDashboardPath(userRole);
      return NextResponse.redirect(new URL(dashboardPath, request.url));
    } else if (authenticated && !userRole) {
      return NextResponse.next();
    } else {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }
  
  if (isPublicRoute(pathname)) {
    if (authenticated && userRole && (pathname === '/login' || pathname === '/signup')) {
      const dashboardPath = getDashboardPath(userRole);
      return NextResponse.redirect(new URL(dashboardPath, request.url));
    }
    if (authenticated && !userRole && (pathname === '/login' || pathname === '/signup')) {
      return NextResponse.next();
    }
    return NextResponse.next();
  }
  
  if (isProtectedRoute(pathname)) {
    if (!authenticated) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    const requiredRole = getRequiredRole(pathname);
    if (requiredRole && userRole && userRole !== requiredRole) {
      const dashboardPath = getDashboardPath(userRole);
      return NextResponse.redirect(new URL(dashboardPath, request.url));
    }

    return NextResponse.next();
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
