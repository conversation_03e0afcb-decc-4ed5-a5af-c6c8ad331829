"use client";

import { useState, useCallback, useEffect, useMemo, useRef } from "react";
import messageService from "@/api/messaging/message.service";
import type {
  Conversation as UIConversation,
  MessagingUser,
  UIMessage,
} from "@/types/messaging";

import { MessageList } from "./components/MessageList";
import { MessageInput } from "./components/MessageInput";
import { ConversationList } from "./components/ConversationList";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/Avatar";
import { useToast } from "@/components/ui/toast";
import { Search, Menu } from "lucide-react";
import { getProfilePhotoUrl } from "@/utils/profilePhoto";

type UserRole = "CLIENT" | "FREELANCER";

export interface MessagingPageProps {
  currentUser: MessagingUser;
  initialConversations?: UIConversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (
    conversationId: string,
    before: Date
  ) => Promise<UIMessage[]>;
  onFileUpload?: (file: File) => Promise<string>;
  className?: string;
  isSending?: boolean;
}

export function MessagingPage({
  currentUser,
  initialConversations = [],
  onFileUpload,
  className,
  isSending = false,
}: MessagingPageProps) {
  const [conversations, setConversations] =
    useState<UIConversation[]>(initialConversations);
  const [selectedConversation, setSelectedConversation] =
    useState<UIConversation | null>(null);

  const initialConversationsRef = useRef(initialConversations);
  
  useEffect(() => {
    console.log("Initial conversations updated:", initialConversations);
    setConversations(initialConversations);
    initialConversationsRef.current = initialConversations;
  }, [initialConversations]);
  
  useEffect(() => {
    if (initialConversationsRef.current.length > 0 && !selectedConversation) {
      setSelectedConversation(initialConversationsRef.current[0]);
    }
  }, [initialConversationsRef, selectedConversation]);
  const [messages, setMessages] = useState<
    Array<UIMessage & { error?: string | null }>
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const [sendStatus, setSendStatus] = useState<{
    type: "idle" | "sending" | "success" | "error";
    message?: string;
  }>({ type: "idle" });

  useEffect(() => {
    if (selectedConversation) {
      console.log("Selected conversation changed:", {
        id: selectedConversation.id,
        participants: selectedConversation.participants.map((participant) => ({
          id: participant.id,
          name: participant.name,
          avatar: participant.avatar,
          role: participant.role,
        })),
        lastMessage: selectedConversation.lastMessage?.content
          ? `${selectedConversation.lastMessage.content.substring(0, 30)}${
              selectedConversation.lastMessage.content.length > 30 ? "..." : ""
            }`
          : "No messages",
      });
    }
  }, [selectedConversation]);

  useEffect(() => {
    const loadMessages = async () => {
      if (!selectedConversation?.id) return;

      setIsLoading(true);
      try {
        if (
          selectedConversation.messages &&
          selectedConversation.messages.length > 0
        ) {
          setMessages(selectedConversation.messages);
        } else {
          setMessages([]);
        }
      } catch (error) {
        console.error("Failed to load messages:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (selectedConversation?.id) {
      loadMessages();
    }
  }, [selectedConversation]);

  const handleSelectConversation = useCallback(
    (conversationId: string) => {
      console.log("handleSelectConversation called with id:", conversationId);
      const conversation = conversations.find((c) => c.id === conversationId);
      console.log("Found conversation:", conversation);
      if (conversation) {
        setSelectedConversation(conversation);
        setIsMobileMenuOpen(false);

        const otherUser = conversation.participants.find(
          (p) => p.id !== currentUser.id
        );
        console.log("Selected conversation with user:", {
          id: otherUser?.id,
          name: otherUser?.name,
          avatar: otherUser?.avatar,
          role: otherUser?.role,
        });
      }
    },
    [conversations, currentUser.id]
  );

  const { showToast } = useToast();

  const handleSendMessage = async (content: string): Promise<void> => {
    if (!selectedConversation || !content.trim() || !currentUser.id) {
      showToast("Error", {
        description: "Cannot send message: Invalid conversation or content",
        variant: "destructive",
      });
      return;
    }

    setSendStatus({ type: "sending", message: "Sending message..." });

    const otherUser = selectedConversation.participants?.find(
      (p) => p.id !== currentUser.id
    );

    if (!otherUser?.id) {
      const errorMsg = "Other user not found in conversation";
      console.error(errorMsg);
      showToast("Error", {
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    const tempId = `temp-${Date.now()}`;
    const now = new Date().toISOString();

    const tempMessage: UIMessage = {
      id: tempId,
      content: content,
      senderId: currentUser.id,
      receiverId: otherUser.id,
      conversationId: selectedConversation.id,
      createdAt: now,
      updatedAt: now,
      status: "sending",
      type: "text",
      fileInfo: undefined,
      sender: {
        id: currentUser.id,
        name: currentUser.name || "You",
        email: currentUser.email || "",
        role: currentUser.role,
        isOnline: true,
        avatar: currentUser.avatar,
      },
      receiver: {
        id: otherUser.id,
        name: otherUser.name || "User",
        email: otherUser.email || "",
        role: otherUser.role,
        isOnline: false,
        avatar: otherUser.avatar,
      },
      isSending: true,
      error: null,
    };

    setMessages((prev) => [...prev, { ...tempMessage }]);

    try {
      const response = await messageService.sendMessage(
        selectedConversation.id,
        currentUser.id,
        otherUser.id,
        content
      );

      const sentMessage: UIMessage = {
        id: response.id,
        content: response.content || content,
        senderId: response.senderId || currentUser.id,
        receiverId: response.receiverId || otherUser.id,
        conversationId: response.conversationId || selectedConversation.id,
        createdAt: response.createdAt || now,
        updatedAt: response.updatedAt || now,
        status: response.status || "delivered",
        type: response.type || "text",
        fileInfo: response.fileInfo,
        sender: response.sender || tempMessage.sender,
        receiver: response.receiver || tempMessage.receiver,
        isSending: false,
        error: null,
      };

      setConversations(prevConversations => 
        prevConversations.map(conv => {
          if (conv.id === selectedConversation?.id) {
            return {
              ...conv,
              lastMessage: {
                id: sentMessage.id,
                content: sentMessage.content,
                senderId: sentMessage.senderId,
                conversationId: sentMessage.conversationId,
                status: "sent",
                type: "text",
                createdAt: sentMessage.createdAt,
              },
              messages: [
                ...(conv.messages || []).filter(msg => msg.id !== tempId),
                sentMessage
              ]
            };
          }
          return conv;
        })
      );

      setMessages(prev => {
        const updatedMessages = prev.filter(msg => msg.id !== tempId);
        return [...updatedMessages, { ...sentMessage, isSending: false }];
      });

      if (selectedConversation) {
        setSelectedConversation(prev => ({
          ...prev!,
          lastMessage: {
            id: sentMessage.id,
            content: sentMessage.content,
            senderId: sentMessage.senderId,
            conversationId: sentMessage.conversationId,
            status: "sent",
            type: "text",
            createdAt: sentMessage.createdAt,
          },
          messages: [
            ...(prev?.messages || []).filter(msg => msg.id !== tempId),
            sentMessage
          ]
        }));
      }
      
      setSendStatus({ type: "success" });
    } catch (error) {
      console.error("Failed to send message:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send message";
      
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === tempId
            ? {
                ...msg,
                status: "error" as const,
                error: errorMessage,
                isSending: false,
              }
            : msg
        )
      );
      
      setSendStatus({ 
        type: "error", 
        message: `Failed to send message: ${errorMessage}` 
      });
      
      showToast("Error", {
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!onFileUpload || !selectedConversation) return;

    try {
      await onFileUpload(file);

      const tempId = `file-${Date.now()}`;
      const now = new Date();
      const receiver = selectedConversation.participants.find(
        (p) => p.id !== currentUser.id
      );
      const receiverId = receiver?.id;
      const conversationId = selectedConversation.id;

      if (!receiverId) {
        throw new Error("Could not determine receiver");
      }

      const fileMessage: UIMessage = {
        id: tempId,
        content: "File attachment",
        senderId: currentUser.id,
        receiverId,
        conversationId,
        status: "sending",
        type: "file",
        fileInfo: {
          name: file.name || "file",
          type: file.type || "application/octet-stream",
          size: file.size,
          url: URL.createObjectURL(file),
        },
        createdAt: now.toISOString(),
        sender: {
          id: currentUser.id,
          name: currentUser.name || "Unknown",
          email: currentUser.email || "",
          role: currentUser.role as UserRole,
          profilePhoto: currentUser.avatar,
          isOnline: currentUser.isOnline,
        },
        receiver: {
          id: receiverId,
          name: receiver.name || "Unknown",
          email: receiver.email || "",
          role: (receiver.role as UserRole) || "CLIENT",
          profilePhoto: receiver.profilePhoto,
          isOnline: receiver.isOnline,
        },
        updatedAt: now.toISOString(),
      };

      setMessages((prev) => [...prev, fileMessage]);
    } catch (error) {
      console.error("Failed to upload file:", error);
    }
  };

  const handleLoadMore = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const filteredConversations = useMemo(
    () =>
      conversations.filter((conv) =>
        conv.participants.some(
          (p: MessagingUser) =>
            p.id !== currentUser.id &&
            p.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      ),
    [conversations, currentUser.id, searchQuery]
  );

  const defaultUser = useMemo(
    () => ({
      id: "unknown",
      name: "Unknown User",
      email: "",
      avatar: undefined,
      role: "CLIENT" as UserRole,
      isOnline: false,
    }),
    []
  );

  const otherUser = useMemo(() => {
    if (!selectedConversation) return defaultUser;

    const user = selectedConversation.participants.find(
      (p) => p.id !== currentUser.id
    );
    if (!user) return defaultUser;

    return {
      id: user.id,
      name: user.name || "Unknown User",
      email: user.email || "",
      avatar: user.avatar,
      role: (user.role as UserRole) || "CLIENT",
      isOnline: user.isOnline || false,
    };
  }, [selectedConversation, currentUser.id, defaultUser]);

  return (
    <div
      className={cn(
        "h-full flex bg-background border rounded-lg overflow-hidden",
        className
      )}
    >
      {/* Conversations Sidebar */}
      <div
        className={cn(
          "w-80 border-r border-border flex flex-col bg-background",
          "lg:flex",
          isMobileMenuOpen
            ? "flex absolute inset-0 z-50 lg:relative lg:inset-auto"
            : "hidden lg:flex"
        )}
      >
        {/* Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-xl font-semibold">Messages</h2>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-muted/50 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            />
          </div>
        </div>

        {/* Conversations List */}
        <ConversationList
          conversations={filteredConversations}
          selectedConversationId={selectedConversation?.id}
          onSelectConversation={handleSelectConversation}
          currentUserId={currentUser.id}
          currentUserRole={currentUser.role as "CLIENT" | "FREELANCER"}
          loading={isLoading && messages.length === 0}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {selectedConversation && otherUser ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-border bg-background">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage
                    src={otherUser.avatar ? getProfilePhotoUrl(otherUser.avatar) : "/logo.png"}
                    alt={otherUser.name}
                  />
                  <AvatarFallback>
                    {otherUser.name?.charAt(0).toUpperCase() || '?'}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{otherUser.name}</h3>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {otherUser.isOnline ? "Online" : "Offline"}
                  </p>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 flex flex-col overflow-hidden">
              <div className="flex-1 overflow-y-auto">
                <MessageList
                  messages={messages.map((msg) => ({
                    id: msg.id,
                    content: msg.content || "",
                    senderId: msg.senderId,
                    receiverId: msg.receiverId || otherUser.id,
                    conversationId:
                      msg.conversationId || selectedConversation?.id || "",
                    createdAt: msg.createdAt || new Date().toISOString(),
                    updatedAt: msg.updatedAt || new Date().toISOString(),
                    status: msg.status || "sent",
                    type: msg.type || "text",
                    sender: {
                      id: msg.senderId,
                      name:
                        msg.senderId === currentUser.id
                          ? currentUser.name
                          : otherUser.name,
                      email:
                        msg.senderId === currentUser.id
                          ? currentUser.email || ""
                          : otherUser.email,
                      role: (msg.senderId === currentUser.id
                        ? currentUser.role
                        : otherUser.role) as "CLIENT" | "FREELANCER",
                      profilePhoto:
                        msg.senderId === currentUser.id
                          ? currentUser.avatar
                          : otherUser.avatar,
                      isOnline:
                        msg.senderId === currentUser.id
                          ? currentUser.isOnline
                          : otherUser.isOnline,
                    },
                    receiver: {
                      id: msg.receiverId || otherUser.id,
                      name:
                        msg.receiverId === currentUser.id
                          ? currentUser.name
                          : otherUser.name,
                      email:
                        msg.receiverId === currentUser.id
                          ? currentUser.email || ""
                          : otherUser.email,
                      role: (msg.receiverId === currentUser.id
                        ? currentUser.role
                        : otherUser.role) as "CLIENT" | "FREELANCER",
                      profilePhoto:
                        msg.receiverId === currentUser.id
                          ? currentUser.avatar
                          : otherUser.avatar,
                      isOnline:
                        msg.receiverId === currentUser.id
                          ? currentUser.isOnline
                          : otherUser.isOnline,
                    },
                    fileInfo: msg.fileInfo,
                    isSending: msg.isSending,
                    error: msg.error,
                  }))}
                  currentUser={currentUser}
                  otherUser={otherUser}
                  loading={isLoading}
                  onLoadMore={handleLoadMore}
                />
              </div>

              {/* Message input with status */}
              <div className="relative border-t bg-background flex-shrink-0">
                {sendStatus.type !== "idle" && (
                  <div
                    className={`px-4 py-1 text-sm ${
                      sendStatus.type === "error"
                        ? "bg-red-100 text-red-800"
                        : sendStatus.type === "success"
                        ? "bg-green-100 text-green-800"
                        : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {sendStatus.message}
                  </div>
                )}
                <div className="p-4">
                  <MessageInput
                    onSend={handleSendMessage}
                    onFileUpload={handleFileUpload}
                    isSending={isSending}
                  />
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileMenuOpen(true)}
              className="lg:hidden mb-4"
            >
              <Menu className="h-5 w-5" />
            </Button>
            <h2 className="text-2xl font-bold mb-2">Your Messages</h2>
            <p className="text-muted-foreground mb-6">
              Select a conversation or start a new one to begin messaging.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
