import { GraphQLError } from 'graphql';
import { UIMessage } from '@/types/messaging';

export interface APIError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: Record<string, unknown>;
}

export const mapGraphQLError = (error: GraphQLError): APIError => {
  let message = 'An unexpected error occurred';
  let code: string | undefined;
  let statusCode = 500;

  if (error.extensions) {
    const { code: errorCode, status, details } = error.extensions as {
      code?: string;
      status?: number;
      details?: Record<string, unknown>;
    };

    if (errorCode) code = errorCode;
    if (status) statusCode = status;
    if (error.message) message = error.message;

    return { message, code, statusCode, details };
  }

  return { message, code, statusCode };
};

export const handleMessageError = (
  error: unknown,
  message: UIMessage,
  setMessages: React.Dispatch<React.SetStateAction<UIMessage[]>>
): void => {
  console.error('Error sending message:', error);
  
  setMessages(prev => prev.map(msg => 
    msg.id === message.id 
      ? { 
          ...msg, 
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to send message'
        } 
      : msg
  ));
};

export const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }
  return 'An unknown error occurred';
};
