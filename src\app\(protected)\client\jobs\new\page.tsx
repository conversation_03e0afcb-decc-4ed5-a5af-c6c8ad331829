'use client';

import { useState } from 'react';
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";

import { jobService } from '@/api/jobs/job.service';
import { CreateJobInput } from "@/types/job";
import { JobForm } from "@/components/jobs/JobForm";

import { Icon } from '@/components/ui/Icon';
import { ContentHeader } from '@/components/layout/ContentHeader';

export default function NewJobPage() {
  const { isAuthenticated, user, cognitoUserId, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);


  const handleSubmit = async (formData: Omit<CreateJobInput, 'clientId'>) => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      console.log('[NewJobPage] Auth state - isAuthenticated:', isAuthenticated);
      console.log('[NewJobPage] User object:', user);
      console.log('[NewJobPage] cognitoUserId:', cognitoUserId);
      console.log('[NewJobPage] cognitoUserId type:', typeof cognitoUserId);
      
      if (!cognitoUserId) {
        const errorMsg = 'User not properly authenticated. Please log in again.';
        console.error('[NewJobPage] Authentication error:', errorMsg);
        throw new Error(errorMsg);
      }

      const jobData: CreateJobInput = {
        ...formData,
        clientId: cognitoUserId
      };

      console.log('[NewJobPage] Creating job with data:', JSON.stringify(jobData, null, 2));
      const createdJob = await jobService.createJob(jobData);
      console.log('[NewJobPage] Job created successfully:', createdJob);
      router.push('/client/jobs');
    } catch (err) {
      console.error('Error creating job:', err);
      setError('Failed to create job. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'CLIENT')) {
    router.push('/login');
    return null;
  }

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Post a New Job"
        subtitle="Fill in the details below to post a new job opportunity."
        breadcrumbs={[
          { label: 'Dashboard', href: '/client/dashboard' },
          { label: 'Jobs', href: '/client/jobs' },
          { label: 'New Job', current: true }
        ]}
        showBackButton={true}
      />
      
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="md" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-card rounded-lg shadow-sm p-0">
        <JobForm 
          clientId={cognitoUserId || ''}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          submitButtonText="Post Job"
          submitButtonClassName="cursor-pointer"
        />
      </div>
    </div>
  );
}
