export const getUser = `
  query GetUser($id: ID!) {
    getUser(id: $id) {
      id
      name
      email
      role
      profilePhoto
      bio
      skills
    }
  }
`;

export const listUsers = `
  query ListUsers(
    $filter: ModelUserFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listUsers(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        email
        role
        profilePhoto
        bio
        skills
      }
      nextToken
    }
  }
`;

export const listUserProfiles = `
  query ListUserProfiles(
    $filter: ModelUserProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listUserProfiles(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        email
        role
        profilePhoto
        bio
        skills
      }
      nextToken
    }
  }
`;
