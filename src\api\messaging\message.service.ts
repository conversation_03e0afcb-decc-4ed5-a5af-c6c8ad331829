import { graphQLClient } from '@/lib/graphql/graphqlClient';
import {
  UIMessage,
  ServerMessage,
  MessagingUser,
  Conversation as UIConversation,
  MessageStatus,
  MessageType,
} from '@/types/messaging';

import {
  LIST_MY_CONVERSATIONS,
  MESSAGES_SUBSCRIPTION
} from './message.queries';

import {
  CREATE_CONVERSATION,
  SEND_MESSAGE,
} from './message.mutations';

export type User = MessagingUser & {
  profilePhoto?: string;
};

export interface ServerConversation {
  id: string;
  jobId: string;
  clientId: string;
  freelancerId: string;
  createdAt: string;
  updatedAt: string;
  messagesData: {
    items: ServerMessage[];
  };
  participants: MessagingUser[];
  client: MessagingUser;
  freelancer: MessagingUser;
  job: {
    id: string;
    title: string;
  };
  lastMessage?: Pick<ServerMessage, 'id' | 'status' | 'type' | 'messageText' | 'createdAt' | 'senderId'> & {
    content: string;
  };
  unreadCount?: number;
}




function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

const messageService = {
  /**
   * Creates a new conversation or returns existing one if it already exists
   */
  async createConversation(jobId: string, clientId: string, freelancerId: string): Promise<string> {
    try {
      const existingConversation = await this.getExistingThread(jobId, clientId, freelancerId);
      if (existingConversation) {
        console.log(`[DEBUG] Conversation already exists with ID: ${existingConversation}`);
        return existingConversation;
      }

      const response = await graphQLClient.execute<{ createConversation: { id: string } }>(
        CREATE_CONVERSATION,
        {
          input: {
            jobId,
            clientId,
            freelancerId,
          },
        }
      );

      if (!response?.createConversation?.id) {
        throw new Error('Failed to create conversation');
      }

      console.log(`[DEBUG] Created new conversation with ID: ${response.createConversation.id}`);
      return response.createConversation.id;
    } catch (error) {
      return handleApiError('create conversation', error);
    }
  },

  /**
   * Sends a message in a conversation
   */
  async sendMessage(
    conversationId: string,
    senderId: string,
    _receiverId: string,
    messageText: string
  ): Promise<UIMessage> {
    try {
      type CreateMessageResponse = {
        createMessage: ServerMessage & {
          sender: {
            id: string;
            name: string;
            email?: string;
            role: string;
            avatar?: string;
          };
          conversationData: {
            clientId: string;
            freelancerId: string;
            client: {
              id: string;
              name: string;
              email?: string;
              profilePhoto?: string;
            };
            freelancer: {
              id: string;
              name: string;
              email?: string;
              profilePhoto?: string;
            };
          };
        };
      };

      const response = await graphQLClient.execute<CreateMessageResponse>(
        SEND_MESSAGE,
        {
          input: {
            conversationId,
            senderId,
            messageText
          },
        }
      );

      if (!response?.createMessage) {
        throw new Error('Failed to send message');
      }

      const message = response.createMessage;
      const isSenderClient: boolean = message.senderId === message.conversationData.clientId;
      const receiver = isSenderClient
        ? message.conversationData.freelancer
        : message.conversationData.client;
      const receiverRole = isSenderClient ? 'FREELANCER' : 'CLIENT';

      return {
        id: message.id,
        content: message.messageText,
        senderId: message.senderId,
        receiverId: receiver.id,
        conversationId: message.conversationId,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        status: message.status || 'delivered',
        type: message.type || 'text',
        fileInfo: message.fileInfo,
        sender: {
          id: message.sender.id,
          name: message.sender.name,
          email: message.sender.email || '',
          role: message.sender.role,
          isOnline: false,
          avatar: message.sender.avatar
        },
        receiver: {
          id: receiver.id,
          name: receiver.name,
          email: receiver.email || '',
          role: receiverRole,
          isOnline: false,
          avatar: receiver.profilePhoto
        }
      };
    } catch (error) {
      return handleApiError('send message', error);
    }
  },

  async listMyConversations(userId: string): Promise<UIConversation[]> {
    try {
      const response = await graphQLClient.execute<{ listConversations: { items: ServerConversation[] } }>(
        LIST_MY_CONVERSATIONS,
        {
          filter: {
            or: [
              { clientId: { eq: userId } },
              { freelancerId: { eq: userId } },
            ],
          },
          userId: userId,
        }
      );

      const items = response?.listConversations?.items || [];

      const mapMessageToUIMessage = (message: ServerMessage, otherParticipant: MessagingUser): UIMessage => ({
        ...message,
        id: message.id,
        content: message.messageText,
        senderId: message.senderId,
        receiverId: otherParticipant.id,
        conversationId: message.conversationId,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        status: message.status || 'delivered',
        type: message.type || 'text',
        fileInfo: message.fileInfo,
        sender: {
          id: message.sender.id,
          name: message.sender.name,
          email: message.sender.email || '',
          role: message.sender.role,
          isOnline: false,
          avatar: message.sender.avatar
        },
        receiver: {
          id: otherParticipant.id,
          name: otherParticipant.name,
          email: otherParticipant.email || '',
          role: otherParticipant.role,
          isOnline: otherParticipant.isOnline || false,
          avatar: otherParticipant.avatar
        }
      });

      const mappedConversations = items.map(conv => {

        return {
          id: conv.id,
          jobId: conv.jobId,
          clientId: conv.clientId,
          freelancerId: conv.freelancerId,
          createdAt: conv.createdAt,
          updatedAt: conv.updatedAt,
          messages: conv.messagesData?.items?.map(msg => {
            const otherParticipant = conv.clientId === msg.senderId ? conv.freelancer : conv.client;
            return mapMessageToUIMessage(msg, {
              id: otherParticipant.id,
              name: otherParticipant.name,
              email: otherParticipant.email || '',
              role: otherParticipant.id === conv.clientId ? 'CLIENT' : 'FREELANCER',
              isOnline: otherParticipant.isOnline || false,
              avatar: otherParticipant.profilePhoto
            });
          }) || [],
          participants: [
            {
              id: conv.client.id,
              name: conv.client.name,
              email: conv.client.email || '',
              role: 'CLIENT' as const,
              isOnline: false,
              avatar: conv.client.profilePhoto
            },
            {
              id: conv.freelancer.id,
              name: conv.freelancer.name,
              email: conv.freelancer.email || '',
              role: 'FREELANCER' as const,
              isOnline: true,
              avatar: conv.freelancer.profilePhoto
            }
          ],
          client: {
            id: conv.client.id,
            name: conv.client.name,
            email: conv.client.email || '',
            role: 'CLIENT' as const,
            isOnline: false,
            avatar: conv.client.profilePhoto
          },
          freelancer: {
            id: conv.freelancer.id,
            name: conv.freelancer.name,
            email: conv.freelancer.email || '',
            role: 'FREELANCER' as const,
            isOnline: true,
            avatar: conv.freelancer.profilePhoto
          },
          job: conv.job ? {
            id: conv.job.id,
            title: conv.job.title || 'No Title'
          } : { id: 'unknown', title: 'No Job' },
          lastMessage: conv.messagesData?.items?.[0] ? {
            id: conv.messagesData.items[0].id,
            content: conv.messagesData.items[0].messageText,
            senderId: conv.messagesData.items[0].senderId,
            conversationId: conv.messagesData.items[0].conversationId,
            createdAt: conv.messagesData.items[0].createdAt,
            status: conv.messagesData.items[0].status || 'delivered',
            type: conv.messagesData.items[0].type || 'text'
          } : undefined,
          unreadCount: conv.unreadCount
        };
      });

      return mappedConversations;
    } catch (error) {
      console.error('[ERROR] Error listing conversations:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  },

  /**
   * Finds an existing conversation thread for the given job and participants
   */
  /**
   * Subscribes to new messages in a conversation
   */
  /**
   * Subscribes to new messages in a conversation
   * @param conversationId - The ID of the conversation to subscribe to
   * @param onMessage - Callback when a new message is received
   * @param onError - Optional error handler
   * @returns An object with an unsubscribe function
   */
  subscribeToMessages(
    conversationId: string,
    onMessage: (message: UIMessage) => void,
    onError?: (error: Error) => void
  ): { unsubscribe: () => void } {
    try {
      type SubscriptionResponse = {
        data?: {
          onCreateMessage?: {
            id: string;
            messageText: string;
            conversationId: string;
            senderId: string;
            receiverId: string;
            status: MessageStatus;
            type: MessageType;
            createdAt: string;
            updatedAt: string;
            sender: MessagingUser;
            receiver: MessagingUser;
          };
        };
      };

      const subscription = (graphQLClient as any).subscribe({
        query: MESSAGES_SUBSCRIPTION,
        variables: { conversationId },
      });

      return subscription.subscribe({
        next: (response: SubscriptionResponse) => {
          if (response?.data?.onCreateMessage) {
            const msg = response.data.onCreateMessage;
            const message: UIMessage = {
              id: msg.id,
              content: msg.messageText,
              messageText: msg.messageText,
              conversationId: msg.conversationId,
              senderId: msg.senderId,
              receiverId: msg.receiverId,
              status: msg.status,
              type: msg.type,
              createdAt: msg.createdAt,
              updatedAt: msg.updatedAt,
              sender: msg.sender,
              receiver: msg.receiver || { 
                id: '', 
                name: 'Unknown', 
                email: '', 
                role: 'CLIENT', 
                isOnline: false 
              }
            };
            onMessage(message);
          }
        },
        error: (error: Error) => {
          console.error('Error in message subscription:', error);
          onError?.(error);
        },
      });
    } catch (error) {
      console.error('Failed to subscribe to messages:', error);
      onError?.(error instanceof Error ? error : new Error('Failed to subscribe to messages'));
      return {
        unsubscribe: () => {},
      };
    }
  },

  async getExistingThread(
    jobId: string,
    clientId: string,
    freelancerId: string
  ): Promise<string | null> {
    try {
      console.log('[DEBUG] Checking for existing thread with:', { jobId, clientId, freelancerId });

      const response = await graphQLClient.execute<{
        listConversations: {
          items: Array<{
            id: string;
            clientId: string;
            freelancerId: string;
          }>
        }
      }>(
        LIST_MY_CONVERSATIONS,
        {
          filter: {
            jobId: { eq: jobId },
            or: [
              {
                and: [
                  { clientId: { eq: clientId } },
                  { freelancerId: { eq: freelancerId } }
                ]
              },
              {
                and: [
                  { clientId: { eq: freelancerId } },
                  { freelancerId: { eq: clientId } }
                ]
              }
            ],
          },
        }
      );

      const existingConversation = response?.listConversations?.items?.find(conv =>
        (conv.clientId === clientId && conv.freelancerId === freelancerId) ||
        (conv.clientId === freelancerId && conv.freelancerId === clientId)
      );

      return existingConversation?.id || null;
    } catch (error) {
      console.error('Error checking for existing thread:', error);
      return null;
    }
  }
};

export default messageService;
