import { cn } from '@/lib/utils';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  asChild?: boolean;
}

const Skeleton = ({
  className,
  asChild = false,
  ...props
}: SkeletonProps) => {
  const Comp = asChild ? 'div' : 'div';
  
  return (
    <Comp
      className={cn(
        'animate-pulse rounded-md bg-muted',
        className
      )}
      {...props}
    />
  );
};

export { Skeleton };
