"use client";

import React from "react";
import { DashboardLayout } from "@/components/layouts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Badge,
} from "@/components/ui";
import { Icon } from "@/components/ui/Icon";

const breadcrumbs = [
  { label: "Dashboard", href: "/freelancer/dashboard" },
  { label: "Overview", current: true },
];

const DashboardExample: React.FC = () => {
  return (
    <DashboardLayout
      breadcrumbs={breadcrumbs}
      title="Dashboard"
      description="Welcome back! Here's what's happening with your freelance work."
      actions={<Button>View All Jobs</Button>}
    >
      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Proposals
            </CardTitle>
            <Icon
              name="FileCheck"
              size="sm"
              className="text-muted-foreground"
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+2 from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              This Month Earnings
            </CardTitle>
            <Icon
              name="DollarSign"
              size="sm"
              className="text-muted-foreground"
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$2,350</div>
            <p className="text-xs text-muted-foreground">
              +15% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Icon
              name="BarChart2"
              size="sm"
              className="text-muted-foreground"
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">73%</div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profile Views</CardTitle>
            <Icon name="Eye" size="sm" className="text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">+12% from last week</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Proposals</CardTitle>
            <CardDescription>Your latest proposal submissions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              {
                title: "E-commerce Website Development",
                status: "pending",
                time: "2 hours ago",
              },
              {
                title: "Mobile App UI Design",
                status: "accepted",
                time: "1 day ago",
              },
              {
                title: "Logo Design Project",
                status: "rejected",
                time: "3 days ago",
              },
            ].map((proposal, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{proposal.title}</p>
                  <p className="text-xs text-muted-foreground">
                    {proposal.time}
                  </p>
                </div>
                <Badge
                  variant={
                    proposal.status === "accepted"
                      ? "success"
                      : proposal.status === "rejected"
                      ? "destructive"
                      : "secondary"
                  }
                >
                  {proposal.status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Available Jobs</CardTitle>
            <CardDescription>
              New opportunities matching your skills
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              {
                title: "React Developer Needed",
                budget: "$500-1000",
                posted: "1 hour ago",
              },
              {
                title: "Content Writer for Blog",
                budget: "$200-500",
                posted: "3 hours ago",
              },
              {
                title: "Social Media Manager",
                budget: "$300-800",
                posted: "5 hours ago",
              },
            ].map((job, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{job.title}</p>
                  <p className="text-xs text-muted-foreground">{job.posted}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{job.budget}</p>
                  <Button size="sm" variant="outline">
                    <Icon name="Eye" size="sm" className="mr-1" />
                    View
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default DashboardExample;
