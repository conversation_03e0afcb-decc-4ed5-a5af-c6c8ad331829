'use client';

import { AnimateOnScroll, AnimateList } from '../common/AnimateOnScroll';
import { animations } from '@/utils/animations';

const demoItems = [
  { id: 1, title: 'Project One', description: 'A brief description of the first project' },
  { id: 2, title: 'Project Two', description: 'A brief description of the second project' },
  { id: 3, title: 'Project Three', description: 'A brief description of the third project' },
];

export function AnimationDemo() {
  return (
    <div className="container mx-auto px-4 py-12">
      {/* Fade in example */}
      <AnimateOnScroll className="mb-16">
        <h2 className="text-3xl font-bold mb-6">Fade In Animation</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          This section fades in smoothly when it comes into view.
        </p>
        <div className="grid gap-6 md:grid-cols-3">
          {[1, 2, 3].map((item) => (
            <div 
              key={item}
              className={`p-6 rounded-lg bg-white dark:bg-gray-800 shadow-md ${animations.cardHover} ${animations.hoverScaleSm}`}
            >
              <h3 className="text-xl font-semibold mb-2">Feature {item}</h3>
              <p className="text-gray-600 dark:text-gray-300">
                This card has a subtle scale effect on hover.
              </p>
            </div>
          ))}
        </div>
      </AnimateOnScroll>

      {/* Slide up example */}
      <AnimateOnScroll animation="slideUp" className="mb-16">
        <h2 className="text-3xl font-bold mb-6">Slide Up Animation</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          This section slides up smoothly when it comes into view.
        </p>
        <div className="p-8 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-xl">
          <p className="text-lg text-center">
            Content with a nice background that slides up into view.
          </p>
        </div>
      </AnimateOnScroll>

      {/* Staggered list example */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold mb-6">Staggered List</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          Items in this list animate in sequence with a slight delay between each.
        </p>
        <AnimateList
          items={demoItems}
          renderItem={(item) => (
            <div 
              className={`p-6 mb-4 rounded-lg bg-white dark:bg-gray-800 shadow-md ${animations.hoverScale} ${animations.cardHover}`}
            >
              <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
              <p className="text-gray-600 dark:text-gray-300">{item.description}</p>
              <a 
                href="#" 
                className={`mt-3 inline-block text-blue-600 dark:text-blue-400 ${animations.linkHover} after:bg-blue-600 dark:after:bg-blue-400`}
              >
                Learn more →
              </a>
            </div>
          )}
          className="space-y-4"
          animation="fadeInUp"
        />
      </div>

      {/* Button with hover effect */}
      <AnimateOnScroll>
        <div className="text-center">
          <button 
            className={`px-6 py-3 bg-blue-600 text-white rounded-lg font-medium ${animations.buttonPress} ${animations.hoverScale} hover:bg-blue-700 transition-colors`}
          >
            Get Started
          </button>
        </div>
      </AnimateOnScroll>
    </div>
  );
}
