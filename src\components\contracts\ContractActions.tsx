"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ui/toast";
import {
  ContractStatus,
  Contract,
  ExtendedContract,
  ContractWorkflowActions,
} from "@/types/features/contracts/contract.types";
import { JobStatus } from "@/types/features/jobs/job.types";
import { UserRole } from "@/types/features/auth/auth.types";
import {
  CheckCircle,
  XCircle,
  Upload,
  ThumbsUp,
  MessageSquare,
  Ban,
  DollarSign,
} from "lucide-react";
import contractService from "@/api/contracts/contract.service";

interface ContractActionsProps {
  contract: Contract | ExtendedContract;
  userRole: UserRole;
  userId: string;
  onStatusUpdate?: (newStatus: ContractStatus) => void;
  onJobStatusUpdate?: (newStatus: JobStatus) => void;
  className?: string;
}

export const ContractActions: React.FC<ContractActionsProps> = ({
  contract,
  userRole,
  userId,
  onStatusUpdate,
  onJobStatusUpdate,
  className = "",
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  const isClient = userRole === UserRole.CLIENT && userId === contract.clientId;
  const isFreelancer =
    userRole === UserRole.FREELANCER && userId === contract.freelancerId;

  // Determine available actions based on contract status and user role
  const getAvailableActions = (): ContractWorkflowActions => {
    const actions: ContractWorkflowActions = {
      canAccept: false,
      canReject: false,
      canSubmitWork: false,
      canApproveWork: false,
      canRequestRevisions: false,
      canCancel: false,
      canMarkPaid: false,
    };

    switch (contract.status) {
      case ContractStatus.PENDING_FREELANCER_ACCEPTANCE:
        if (isFreelancer) {
          actions.canAccept = true;
          actions.canReject = true;
        }
        break;

      case ContractStatus.ACTIVE:
        if (isFreelancer) {
          actions.canSubmitWork = true;
        }
        if (isClient || isFreelancer) {
          actions.canCancel = true;
        }
        break;

      case ContractStatus.WORK_SUBMITTED:
        if (isClient) {
          actions.canApproveWork = true;
          actions.canRequestRevisions = true;
        }
        break;

      case ContractStatus.REVISIONS_REQUESTED:
        if (isFreelancer) {
          actions.canSubmitWork = true;
        }
        if (isClient || isFreelancer) {
          actions.canCancel = true;
        }
        break;

      case ContractStatus.COMPLETED:
        if (isClient) {
          actions.canMarkPaid = true;
        }
        break;
    }

    return actions;
  };

  const actions = getAvailableActions();

  const handleAcceptContract = async () => {
    try {
      setIsLoading(true);

      // Update contract status to ACTIVE
      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.ACTIVE
      );

      // Update job status to IN_PROGRESS
      if (contract.jobId) {
        await contractService.updateJobStatus(
          contract.jobId,
          JobStatus.IN_PROGRESS
        );
        onJobStatusUpdate?.(JobStatus.IN_PROGRESS);
      }

      onStatusUpdate?.(ContractStatus.ACTIVE);

      showToast("Success", {
        description:
          "Contract accepted successfully! You can now start working.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error accepting contract:", error);
      showToast("Error", {
        description: "Failed to accept contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectContract = async () => {
    try {
      setIsLoading(true);

      // Update contract status to CANCELLED
      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.CANCELLED
      );

      // Update job status back to OPEN
      if (contract.jobId) {
        await contractService.updateJobStatus(contract.jobId, JobStatus.OPEN);
        onJobStatusUpdate?.(JobStatus.OPEN);
      }

      onStatusUpdate?.(ContractStatus.CANCELLED);

      showToast("Success", {
        description: "Contract declined successfully.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error rejecting contract:", error);
      showToast("Error", {
        description: "Failed to decline contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelContract = async () => {
    try {
      setIsLoading(true);

      // Update contract status to CANCELLED
      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.CANCELLED
      );

      // Update job status to CANCELLED
      if (contract.jobId) {
        await contractService.updateJobStatus(
          contract.jobId,
          JobStatus.CANCELLED
        );
        onJobStatusUpdate?.(JobStatus.CANCELLED);
      }

      onStatusUpdate?.(ContractStatus.CANCELLED);

      showToast("Success", {
        description: "Contract cancelled successfully.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error cancelling contract:", error);
      showToast("Error", {
        description: "Failed to cancel contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveWork = async () => {
    try {
      setIsLoading(true);

      // Update contract status to COMPLETED
      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.COMPLETED
      );

      // Update job status to COMPLETED
      if (contract.jobId) {
        await contractService.updateJobStatus(
          contract.jobId,
          JobStatus.COMPLETED
        );
        onJobStatusUpdate?.(JobStatus.COMPLETED);
      }

      onStatusUpdate?.(ContractStatus.COMPLETED);

      showToast("Success", {
        description: "Work approved successfully! Contract is now completed.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error approving work:", error);
      showToast("Error", {
        description: "Failed to approve work. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestRevisions = async () => {
    try {
      setIsLoading(true);

      // Update contract status to REVISIONS_REQUESTED
      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.REVISIONS_REQUESTED
      );

      onStatusUpdate?.(ContractStatus.REVISIONS_REQUESTED);

      showToast("Success", {
        description: "Revision request sent to freelancer.",
        variant: "success",
      });
    } catch (error) {
      console.error("Error requesting revisions:", error);
      showToast("Error", {
        description: "Failed to request revisions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkPaid = async () => {
    try {
      setIsLoading(true);

      const amount = "budget" in contract ? contract.budget : 0;

      await contractService.createPayment(contract.id, amount);

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.PAID
      );

      onStatusUpdate?.(ContractStatus.PAID);

      showToast("Success", {
        description: "Payment processed successfully!",
        variant: "success",
      });
    } catch (error) {
      console.error("Error processing payment:", error);
      showToast("Error", {
        description: "Failed to process payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isClient && !isFreelancer) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {actions.canAccept && (
        <Button
          onClick={handleAcceptContract}
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="mr-2 h-4 w-4" />
          Accept Contract
        </Button>
      )}

      {actions.canReject && (
        <Button
          variant="outline"
          onClick={handleRejectContract}
          disabled={isLoading}
        >
          <XCircle className="mr-2 h-4 w-4" />
          Decline Contract
        </Button>
      )}

      {actions.canSubmitWork && (
        <Button
          onClick={() => {
            // This will be handled by the WorkSubmissionForm component
            showToast("Info", {
              description:
                "Use the work submission form below to submit your work.",
              variant: "default",
            });
          }}
          disabled={isLoading}
        >
          <Upload className="mr-2 h-4 w-4" />
          Submit Work
        </Button>
      )}

      {actions.canApproveWork && (
        <Button
          onClick={handleApproveWork}
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700"
        >
          <ThumbsUp className="mr-2 h-4 w-4" />
          Approve Work
        </Button>
      )}

      {actions.canRequestRevisions && (
        <Button
          variant="outline"
          onClick={handleRequestRevisions}
          disabled={isLoading}
        >
          <MessageSquare className="mr-2 h-4 w-4" />
          Request Changes
        </Button>
      )}

      {actions.canCancel && (
        <Button
          variant="destructive"
          onClick={handleCancelContract}
          disabled={isLoading}
        >
          <Ban className="mr-2 h-4 w-4" />
          Cancel Contract
        </Button>
      )}

      {actions.canMarkPaid && (
        <Button
          onClick={handleMarkPaid}
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <DollarSign className="mr-2 h-4 w-4" />
          Mark as Paid
        </Button>
      )}
    </div>
  );
};

export default ContractActions;
