"use client";
import React, { useEffect, useState } from "react";
import { DashboardLayout } from "@/components/layouts/DashboardLayout";
import ProfilePhotoUploader from "@/components/layout/profile/ProfilePhotoUploader";
import { useAuth } from "@/lib/auth/AuthContext";
import { getUserById, updateUser } from "@/lib/user";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Label } from "@/components/ui/Form";
import { Icon } from "@/components/ui/Icon";

export default function ProfilePage() {
  const { user, updateProfile, cognitoUserId, refresh, isAuthenticated } =
    useAuth();
  const [name, setName] = useState("");
  const [bio, setBio] = useState("");
  const [skills, setSkills] = useState<string[]>([]);
  const [profilePhoto, setProfilePhoto] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const loadUserProfile = async () => {
      if (!cognitoUserId) {
        return;
      }

      try {
        setIsLoading(true);
        const profile = await getUserById(cognitoUserId);

        if (profile) {
          setName(profile.name || "");
          setBio(profile.bio || "");
          setSkills(Array.isArray(profile.skills) ? profile.skills : []);
          setProfilePhoto(profile.profilePhoto || null);
        }
      } catch (err) {
        console.error("Error loading user profile:", err);
        if (user) {
          setName(user.attributes?.name || "");
          setBio("");
          setSkills([]);
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (cognitoUserId) {
      loadUserProfile();
    } else if (user) {
      setName(user.attributes?.name || "");
      setBio("");
      setSkills([]);
      setIsLoading(false);
    }
  }, [cognitoUserId, user]);

  if (isLoading || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  const onSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsError(false);
    setIsSaving(true);

    if (!cognitoUserId) {
      setIsError(true);
      setIsSaving(false);
      return;
    }

    try {
      const updatedSkills = skills.filter(Boolean);

      await updateUser({
        id: cognitoUserId,
        name: name || null,
        bio: bio || null,
        skills: updatedSkills.length > 0 ? updatedSkills : null,
      });

      await updateProfile({ name, bio, skills: updatedSkills });
      await refresh();
    } catch (e) {
      setIsError(true);
      console.error("Error updating profile:", e);
    } finally {
      setIsSaving(false);
    }
  };

  const userRole = user.attributes?.["custom:role"] || "CLIENT";
  const breadcrumbs = [
    { label: "Home", href: `/${userRole.toLowerCase()}/dashboard` },
    { label: "Profile", href: "/profile" },
  ];

  return (
    <DashboardLayout
      breadcrumbs={breadcrumbs}
      title="Profile"
      description="Manage your profile information and settings"
    >
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 py-8">
        <div className="space-y-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-foreground">
              Your Profile
            </h1>
            <p className="mt-2 text-muted-foreground">
              Update your personal information and profile settings
            </p>
          </div>

          {isError && (
            <div className="p-4 mb-6 rounded-md bg-red-100 text-red-700">
              <p>An error occurred. Please try again.</p>
            </div>
          )}

          <div className="space-y-6">
            <Card className="bg-card rounded-lg shadow-sm border">
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  Profile Photo
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Upload and manage your profile picture
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <ProfilePhotoUploader 
                    userId={cognitoUserId || ''}
                    currentPhotoUrl={profilePhoto || (Array.isArray(user?.attributes?.picture) ? user.attributes.picture[0] : user?.attributes?.picture) || ''}
                    onPhotoUpdate={(newPhotoUrl) => {
                      setProfilePhoto(newPhotoUrl);
                    }}
                    size={160}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card rounded-lg shadow-sm border">
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  Personal Information
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Update your personal details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={onSave} className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-medium">
                        Full Name
                      </Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Enter your full name"
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio" className="text-sm font-medium">
                        About You
                      </Label>
                      <Textarea
                        id="bio"
                        value={bio}
                        onChange={(e) => setBio(e.target.value)}
                        placeholder="Tell us about yourself, your experience, and your skills"
                        rows={4}
                        className="min-h-[100px]"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="skills" className="text-sm font-medium">
                        Skills
                      </Label>
                      <Input
                        id="skills"
                        value={skills?.join(", ") || ""}
                        onChange={(e) => setSkills(e.target.value.split(",").map((s: string) => s.trim()).filter(Boolean) || [])}
                        placeholder="e.g., Web Development, Design, Marketing, Writing"
                        className="w-full"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Separate skills with commas
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" className="w-full sm:w-auto" disabled={isSaving}>
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>

            {cognitoUserId && (
              <Card className="bg-card rounded-lg shadow-sm border">
                <CardHeader>
                  <CardTitle className="text-lg font-medium">
                    Account Information
                  </CardTitle>
                  <CardDescription className="text-muted-foreground">
                    Your account details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between py-2">
                      <span className="text-sm font-medium text-muted-foreground">
                        Email
                      </span>
                      <span className="text-sm">
                        {user?.attributes?.email || "Not available"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <span className="text-sm font-medium text-muted-foreground">
                        Account Type
                      </span>
                      <span className="text-sm capitalize">
                        {userRole.toLowerCase()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
