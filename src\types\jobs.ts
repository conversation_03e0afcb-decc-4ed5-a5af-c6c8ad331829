import { Job<PERSON>ategory, JobStatus, ProposalStatus } from './enums';

/**
 * Core job-related types and interfaces
 */

export interface Job {
  /** Unique identifier for the job */
  id: string;
  
  /** Job title */
  title: string;
  
  /** Detailed job description */
  description: string;
  
  /** Job category */
  category: JobCategory;
  
  /** Budget for the job */
  budget: number;
  
  /** Deadline for the job (ISO date string) */
  deadline: string;
  
  /** ID of the client who posted the job */
  clientId: string;
  
  /** Name of the client who posted the job (optional) */
  clientName?: string;
  
  /** Current status of the job */
  status: JobStatus;
  
  /** Whether the job is remote */
  isRemote?: boolean;
  
  /** Required skills for the job */
  skills?: string[];
  
  /** Job location (if not remote) */
  location?: string;
  
  /** When the job was created (ISO date string) */
  createdAt: string;
  
  /** When the job was last updated (ISO date string) */
  updatedAt?: string;
}

/**
 * Job with its associated proposals
 */
export interface JobWithProposals extends Job {
  /** List of proposals for this job */
  proposals: JobProposal[];
  
  /** Number of proposals for this job */
  proposalCount: number;
}

/**
 * Input type for creating a new job
 */
export interface CreateJobInput {
  title: string;
  description: string;
  budget: number;
  category: JobCategory;
  deadline: string;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
}

/**
 * Input type for updating an existing job
 */
export interface UpdateJobInput extends Partial<Omit<CreateJobInput, 'clientId'>> {
  id: string;
}

/**
 * Filter criteria for querying jobs
 */
export interface JobFilter {
  category?: JobCategory;
  minBudget?: number;
  maxBudget?: number;
  status?: JobStatus;
  searchTerm?: string;
  clientId?: string;
  isRemote?: boolean;
  skills?: string[];
}

/**
 * Represents a job proposal from a freelancer
 */
export interface JobProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  freelancerName?: string;
  coverLetter?: string;
  bidAmount: number;
  proposedRate?: number;
  status: ProposalStatus;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Input for creating a new job proposal
 */
export interface CreateJobProposalInput {
  jobId: string;
  coverLetter?: string;
  bidAmount: number;
  proposedRate?: number;
}

/**
 * Job with proposal count and optional proposals
 */
export interface JobWithProposalCount extends Job {
  proposalCount: number;
  proposals?: JobProposal[];
}
