import { AuthMode } from './enums';

/**
 * Interface for the minimal GraphQL client functionality needed
 */
/**
 * Interface for the GraphQL client with type-safe request/response handling
 */
export interface AmplifyGraphQLClient {
  /**
   * Execute a GraphQL query/mutation
   */
  graphql: <T = unknown, V extends GraphQLVariables = GraphQLVariables>(
    options: GraphQLRequestOptions<V>
  ) => Promise<GraphQLResult<T>>;
}

/**
 * Type for GraphQL variables
 */
export type GraphQLVariables = Record<string, unknown>;

/**
 * Type for GraphQL response with data and errors
 * @template T The expected shape of the response data
 */
export interface GraphQLResult<T = unknown> {
  /** The data returned by the GraphQL operation */
  data?: T;
  
  /** Any errors that occurred during the operation */
  errors?: Array<{ 
    /** Human-readable error message */
    message: string;
    
    /** Optional error extensions for additional context */
    extensions?: Record<string, unknown>;
    
    /** Path to the field that caused the error */
    path?: Array<string | number>;
  }>;
  
  /** Optional message for network or other non-GraphQL errors */
  message?: string;
}

/**
 * Type for GraphQL request options
 * @template V The type of variables expected by the query
 */
export interface GraphQLRequestOptions<V = GraphQLVariables> {
  /** The GraphQL query or mutation string */
  query: string;
  
  /** Variables to pass to the GraphQL operation */
  variables?: V;
  
  /** Authentication mode for the request */
  authMode?: AuthMode;
  
  /** Optional authentication token */
  authToken?: string;
}
