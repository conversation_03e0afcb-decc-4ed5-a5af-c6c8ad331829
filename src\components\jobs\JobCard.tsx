import React from 'react';
import Link from 'next/link';
import { Job } from '@/types/job';
import { Loader2 } from 'lucide-react';
import { Icon } from '@/components/ui/Icon';
import { formatDistanceToNow } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Badge } from '@/components/ui';
import { cn, formatCurrency } from '@/lib/utils';
import { JobStatus } from '@/types/enums';

interface JobCardProps {
  job: Job;
  showClientInfo?: boolean;
  showActions?: boolean;
  onDelete?: (id: string) => void;
  onEdit?: (job: Job) => void;
  deleteLoading?: boolean;
}

export const JobCard: React.FC<JobCardProps> = ({
  job,
  showClientInfo = true,
  showActions = false,
  onDelete,
  onEdit,
  deleteLoading = false
}) => {
  const formattedDate = job.createdAt ? formatDistanceToNow(new Date(job.createdAt), { addSuffix: true }) : '';
  const deadlineDate = job.deadline ? new Date(job.deadline).toLocaleDateString() : 'No deadline';

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg">
              <Link href={`/jobs/${job.id}`} className="hover:text-primary transition-colors">
                {job.title}
              </Link>
            </CardTitle>

            {showClientInfo && formattedDate && (
              <CardDescription className="mt-1">
                Posted {formattedDate}
              </CardDescription>
            )}
          </div>

          {showActions && onEdit && onDelete && (
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onEdit(job)}
                aria-label="Edit job"
              >
                <Icon name="Edit" size="sm" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(job.id)}
                className="text-destructive hover:bg-destructive/10"
                aria-label="Delete job"
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <Loader2 size="sm" className="animate-spin" />
                ) : (
                  <Icon name="Trash2" size="sm"/>
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* Job metadata */}
          <div className="flex flex-wrap items-center gap-2 text-sm">
            <Badge 
              variant="outline" 
              className={cn(
                'font-medium',
                job.status === JobStatus.OPEN && 'bg-green-50 text-green-700 border-green-200',
                job.status === JobStatus.IN_PROGRESS && 'bg-blue-50 text-blue-700 border-blue-200',
                job.status === JobStatus.COMPLETED && 'bg-purple-50 text-purple-700 border-purple-200',
                job.status === JobStatus.CANCELLED && 'bg-gray-100 text-gray-600 border-gray-200'
              )}
            >
              {job.status?.replace('_', ' ') || 'N/A'}
            </Badge>
            {job.category && (
              <Badge variant="secondary">
                {job.category.replace('_', ' ')}
              </Badge>
            )}
            {job.budget && (
              <span className="text-muted-foreground">
                {formatCurrency(job.budget)}
              </span>
            )}
            <span className="text-muted-foreground">•</span>
            <span className="text-muted-foreground">
              Deadline: {deadlineDate}
            </span>
          </div>

          {/* Job description */}
          <p className="text-muted-foreground line-clamp-2">
            {job.description}
          </p>

          {/* Actions */}
          <div className="flex items-center justify-between pt-2">
            <Badge variant="outline">
              {job.category || 'Job'}
            </Badge>

            <Button variant="link" size="sm" asChild>
              <Link href={`/jobs/${job.id}`}>
                View Details <Icon name="ArrowRight" size="sm" className="ml-1" />
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JobCard;
