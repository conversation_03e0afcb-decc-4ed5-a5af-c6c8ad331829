import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/api';
import { getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';
import { deleteProfilePhotoFromS3, checkIfFileExists } from '../utils/profilePhoto';
import { updateUser } from '../lib/graphql/mutations';
import type { User } from '../types/API';
import S3Service from '@/lib/s3/s3Service';

type UploadProgress = {
  loaded: number;
  total: number;
};

type GraphQLResult<T> = {
  data?: T;
  errors?: any[];
  extensions?: {
    [key: string]: any;
  };
};

type UseProfilePhotoReturn = {
  isUploading: boolean;
  uploadError: Error | null;
  uploadProgress: number;
  uploadProfilePhoto: (file: File, userId: string, currentPhotoUrl?: string | null) => Promise<string | null>;
  deleteProfilePhoto: (photoKey: string, userId: string) => Promise<void>;
};

export const useProfilePhoto = (): UseProfilePhotoReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<Error | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const user = await getCurrentUser();
        const attributes = await fetchUserAttributes();
        setCurrentUser({ ...user, ...attributes });
      } catch (error) {
        console.error('Error loading user:', error);
      }
    };
    
    loadUser();
  }, []);

  const uploadProfilePhoto = async (file: File, userId: string, currentPhotoUrl?: string | null): Promise<string | null> => {
    if (!file) {
      console.error('No file provided for upload');
      return null;
    }
    
    if (!currentUser) {
      console.error('No current user found');
      return null;
    }

    const validUserId = userId || currentUser.userId || currentUser.sub || 'unknown';
    if (!validUserId || validUserId === 'unknown') {
      console.error('Invalid user ID for file upload', { userId, currentUser });
      return null;
    }

    setIsUploading(true);
    setUploadError(null);
    setUploadProgress(0);

    let oldPhotoKey: string | null = null;
    
    try {
      if (currentPhotoUrl) {
        try {
          if (currentPhotoUrl.startsWith('http')) {
            const url = new URL(currentPhotoUrl);
            const pathParts = url.pathname.split('/');
            const fileNameWithParams = pathParts[pathParts.length - 1];
            const fileName = fileNameWithParams.split('?')[0];
            oldPhotoKey = `profile-photos/${fileName}`;
          } else {
            oldPhotoKey = `profile-photos/${currentPhotoUrl.split('/').pop()?.split('?')[0]}`;
          }
        } catch (error) {
          console.warn('Error parsing photo URL:', error);
          const fileName = currentPhotoUrl.split('/').pop()?.split('?')[0];
          if (fileName) {
            oldPhotoKey = `profile-photos/${fileName}`;
          }
        }
      }

      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const fileName = `user-${validUserId}-${Date.now()}.${fileExtension}`;
      const s3Key = `profile-photos/${fileName}`;
      
      console.log('Uploading new profile photo with S3 key:', s3Key);
      if (oldPhotoKey) {
        console.log('Will remove old profile photo after successful upload:', oldPhotoKey);
      }

      await S3Service.uploadFile({
        file,
        fileName: s3Key,
        progressCallback: (progress: UploadProgress) => {
          const percentComplete = Math.round((progress.loaded / progress.total) * 100);
          setUploadProgress(percentComplete);
        },
      });

      console.log('Successfully uploaded new profile photo:', fileName);

      const client = generateClient();
      const updateInput = {
        id: validUserId,
        profilePhoto: fileName,
        name: currentUser.name || currentUser.given_name || 'User',
        email: currentUser.email || '',
        role: currentUser['custom:role'] || currentUser.role || 'CLIENT',
        bio: currentUser.bio || '',
        skills: currentUser.skills || [],
      };

      const result = await client.graphql({
        query: updateUser,
        variables: { input: updateInput },
        authMode: 'userPool',
      }) as GraphQLResult<{ updateUser: User }>;

      if (result.errors) {
        throw new Error(result.errors[0].message || 'Failed to update profile photo');
      }

      const photoUrl = await S3Service.getFileUrl({ key: s3Key });
      
      setCurrentUser({
        ...currentUser,
        profilePhoto: photoUrl
      });
      
      if (oldPhotoKey && oldPhotoKey !== s3Key) {
        try {
          await S3Service.deleteFile({ key: oldPhotoKey });
          console.log('Successfully deleted old profile photo:', oldPhotoKey);
        } catch (deleteError) {
          console.error('Error deleting old profile photo:', deleteError);
        }
      }
      
      window.dispatchEvent(new CustomEvent('profilePhotoUpdated'));
      
      return photoUrl;
    } catch (error) {
      console.error('Error uploading profile photo:', error);
      setUploadError(error as Error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const deleteProfilePhoto = async (photoKey: string, userId: string): Promise<void> => {
    if (!photoKey) {
      console.error('No photo key provided for deletion');
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      console.log('Attempting to delete photo from S3 with key:', photoKey);
      
      try {
        await deleteProfilePhotoFromS3(photoKey);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const fileStillExists = await checkIfFileExists(photoKey);
        if (fileStillExists) {
          console.warn('File still exists after deletion, S3 propagation delay may be in effect');
        }
        
      } catch (error) {
        console.error('❌ Failed to delete or verify photo deletion from S3:', error);
        throw new Error('Failed to delete photo from storage. Please try again.');
      }
      
      const client = generateClient();
      const updateInput = {
        id: userId,
        profilePhoto: null,
        name: currentUser?.name || currentUser?.given_name || 'User',
        email: currentUser?.email || '',
        role: currentUser?.['custom:role'] || currentUser?.role || 'CLIENT',
        bio: currentUser?.bio || '',
        skills: currentUser?.skills || [],
      };

      console.log('Updating user profile to remove photo reference');
      const result = await client.graphql({
        query: updateUser,
        variables: { input: updateInput },
        authMode: 'userPool',
      }) as GraphQLResult<{ updateUser: User }>;

      if (result.errors) {
        console.error('❌ Failed to update user profile:', result.errors);
        throw new Error('Photo was deleted but we failed to update your profile. Please refresh the page.');
      }
      
      if (currentUser) {
        const updatedUser = {
          ...currentUser,
          profilePhoto: null,
        };
        setCurrentUser(updatedUser);
      }

      window.dispatchEvent(new CustomEvent('profilePhotoUpdated'));
      
      console.log('Successfully deleted profile photo');
    } catch (error) {
      console.error('Error in deleteProfilePhoto:', error);
      setUploadError(error as Error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  return {
    isUploading,
    uploadError,
    uploadProgress,
    uploadProfilePhoto,
    deleteProfilePhoto,
  };
};

export default useProfilePhoto;
