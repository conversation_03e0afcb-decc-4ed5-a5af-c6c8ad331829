/**
 * Common enums used throughout the application
 * Follows SCREAMING_SNAKE_CASE convention for enum members
 */

export enum JobCategory {
  WEB_DEVELOPMENT = 'WEB_DEVELOPMENT',
  MOBILE_DEVELOPMENT = 'MO<PERSON>LE_DEVELOPMENT',
  DESIGN = 'DESIGN',
  BUSINESS = 'BUSINESS',
  OTHER = 'OTHER'
}

export enum JobStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum ProposalStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED'
}

export enum UserRole {
  CLIENT = 'CLIENT',
  FREELANCER = 'FREELANCER',
  ADMIN = 'ADMIN'
}

export enum AuthMode {
  API_KEY = 'API_KEY',
  USER_POOL = 'USER_POOL',
  IAM = 'IAM',
  OIDC = 'OIDC',
  LAMBDA = 'LAMBDA',
  NONE = 'NONE'
}

export enum IconSize {
  SM = 'sm',
  MD = 'md',
  LG = 'lg',
  XL = 'xl',
  '2XL' = '2xl'
}
